{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBRdWVzdGlvbkNhcmQgZnJvbSAnLi9jb21wb25lbnRzL1F1ZXN0aW9uQ2FyZCcKaW1wb3J0IFF1ZXN0aW9uRm9ybSBmcm9tICcuL2NvbXBvbmVudHMvUXVlc3Rpb25Gb3JtJwppbXBvcnQgQmF0Y2hJbXBvcnQgZnJvbSAnLi9jb21wb25lbnRzL0JhdGNoSW1wb3J0JwppbXBvcnQgeyBsaXN0UXVlc3Rpb24sIGRlbFF1ZXN0aW9uLCBnZXRRdWVzdGlvblN0YXRpc3RpY3MgfSBmcm9tICdAL2FwaS9iaXovcXVlc3Rpb24nCmltcG9ydCB7IGJhdGNoSW1wb3J0UXVlc3Rpb25zIH0gZnJvbSAnQC9hcGkvYml6L3F1ZXN0aW9uQmFuaycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUXVlc3Rpb25CYW5rRGV0YWlsIiwKICBjb21wb25lbnRzOiB7CiAgICBRdWVzdGlvbkNhcmQsCiAgICBRdWVzdGlvbkZvcm0sCiAgICBCYXRjaEltcG9ydAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmimOW6k+S/oeaBrwogICAgICBiYW5rSWQ6IG51bGwsCiAgICAgIGJhbmtOYW1lOiAnJywKICAgICAgLy8g57uf6K6h5pWw5o2uCiAgICAgIHN0YXRpc3RpY3M6IHsKICAgICAgICB0b3RhbDogMCwKICAgICAgICBzaW5nbGVDaG9pY2U6IDAsCiAgICAgICAgbXVsdGlwbGVDaG9pY2U6IDAsCiAgICAgICAganVkZ21lbnQ6IDAKICAgICAgfSwKICAgICAgLy8g6aKY55uu5YiX6KGoCiAgICAgIHF1ZXN0aW9uTGlzdDogW10sCiAgICAgIC8vIOWIhumhteWPguaVsAogICAgICB0b3RhbDogMCwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBiYW5rSWQ6IG51bGwsCiAgICAgICAgcXVlc3Rpb25UeXBlOiBudWxsLAogICAgICAgIGRpZmZpY3VsdHk6IG51bGwsCiAgICAgICAgcXVlc3Rpb25Db250ZW50OiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOWxleW8gOeKtuaAgQogICAgICBleHBhbmRBbGw6IGZhbHNlLAogICAgICAvLyDpgInmi6nnirbmgIEKICAgICAgc2VsZWN0ZWRRdWVzdGlvbnM6IFtdLAogICAgICAvLyDpgInmi6nnm7jlhbMKICAgICAgc2VsZWN0ZWRRdWVzdGlvbnM6IFtdLAogICAgICBpc0FsbFNlbGVjdGVkOiBmYWxzZSwKICAgICAgZXhwYW5kZWRRdWVzdGlvbnM6IFtdLAogICAgICAvLyDooajljZXnm7jlhbMKICAgICAgcXVlc3Rpb25Gb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRRdWVzdGlvblR5cGU6ICdzaW5nbGUnLAogICAgICBjdXJyZW50UXVlc3Rpb25EYXRhOiBudWxsLAogICAgICAvLyDmibnph4/lr7zlhaUKICAgICAgaW1wb3J0RHJhd2VyVmlzaWJsZTogZmFsc2UsCiAgICAgIGJhdGNoSW1wb3J0VmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRJbXBvcnRNb2RlOiAnZXhjZWwnLCAvLyDlvZPliY3lr7zlhaXmqKHlvI8KICAgICAgLy8g5paH5qGj5a+85YWl5oq95bGJCiAgICAgIGRvY3VtZW50Q29udGVudDogJycsCiAgICAgIGRvY3VtZW50SHRtbENvbnRlbnQ6ICcnLCAvLyDlrZjlgqjlr4zmlofmnKxIVE1M5YaF5a6555So5LqO6aKE6KeICiAgICAgIHBhcnNlZFF1ZXN0aW9uczogW10sCiAgICAgIHBhcnNlRXJyb3JzOiBbXSwKICAgICAgLy8g5YWo6YOo5bGV5byAL+aUtui1t+eKtuaAgQogICAgICBhbGxFeHBhbmRlZDogdHJ1ZSwKICAgICAgLy8g5qCH5b+X5L2N77ya5piv5ZCm5q2j5Zyo5LuO5ZCO56uv6K6+572u5YaF5a6577yI6YG/5YWN6Kem5Y+R5YmN56uv6YeN5paw6Kej5p6Q77yJCiAgICAgIGlzU2V0dGluZ0Zyb21CYWNrZW5kOiBmYWxzZSwKICAgICAgbGFzdFNhdmVUaW1lOiAnJywKICAgICAgLy8g57yT5a2Y55u45YWzCiAgICAgIGNhY2hlS2V5OiAncXVlc3Rpb25CYW5rX2RyYWZ0X2NvbnRlbnQnLAogICAgICBhdXRvU2F2ZVRpbWVyOiBudWxsLAogICAgICBoYXNVbnNhdmVkQ2hhbmdlczogZmFsc2UsCiAgICAgIGRvY3VtZW50SW1wb3J0RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHJ1bGVzRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIC8vIOinhOiMg+Wvueivneahhuagh+etvumhtQogICAgICBhY3RpdmVSdWxlVGFiOiAnZXhhbXBsZXMnLAogICAgICAvLyDkuIrkvKDlkozop6PmnpDnirbmgIEKICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLAogICAgICBpc1BhcnNpbmc6IGZhbHNlLAogICAgICBpbXBvcnRPcHRpb25zOiB7CiAgICAgICAgcmV2ZXJzZTogZmFsc2UsCiAgICAgICAgYWxsb3dEdXBsaWNhdGU6IGZhbHNlCiAgICAgIH0sCiAgICAgIC8vIOaWh+S7tuS4iuS8oAogICAgICB1cGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL2Jpei9xdWVzdGlvbkJhbmsvdXBsb2FkRG9jdW1lbnQnLAogICAgICB1cGxvYWRIZWFkZXJzOiB7CiAgICAgICAgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgdGhpcy4kc3RvcmUuZ2V0dGVycy50b2tlbgogICAgICB9LAogICAgICB1cGxvYWREYXRhOiB7fSwKICAgICAgLy8g5a+M5paH5pys57yW6L6R5ZmoCiAgICAgIHJpY2hFZGl0b3I6IG51bGwsCiAgICAgIGVkaXRvckluaXRpYWxpemVkOiBmYWxzZQogICAgfQogIH0sCgogIHdhdGNoOiB7CiAgICAvLyDnm5HlkKzmlofmoaPlhoXlrrnlj5jljJbvvIzoh6rliqjop6PmnpAKICAgIGRvY3VtZW50Q29udGVudDogewogICAgICBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIC8vIOWmguaenOaYr+S7juWQjuerr+iuvue9ruWGheWuue+8jOS4jeinpuWPkeWJjeerr+ino+aekAogICAgICAgIGlmICh0aGlzLmlzU2V0dGluZ0Zyb21CYWNrZW5kKSB7CiAgICAgICAgICByZXR1cm4KICAgICAgICB9CgoKCiAgICAgICAgaWYgKG5ld1ZhbCAmJiBuZXdWYWwudHJpbSgpKSB7CiAgICAgICAgICB0aGlzLmRlYm91bmNlUGFyc2VEb2N1bWVudCgpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSBbXQogICAgICAgIH0KICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiBmYWxzZQogICAgfSwKICAgIC8vIOebkeWQrOaKveWxieaJk+W8gOeKtuaAgQogICAgaW1wb3J0RHJhd2VyVmlzaWJsZTogewogICAgICBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIGlmIChuZXdWYWwpIHsKICAgICAgICAgIC8vIOaKveWxieaJk+W8gOaXtuWIneWni+WMlue8lui+keWZqAogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICB0aGlzLmluaXRSaWNoRWRpdG9yKCkKICAgICAgICAgIH0pCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOaKveWxieWFs+mXreaXtumUgOavgee8lui+keWZqAogICAgICAgICAgaWYgKHRoaXMucmljaEVkaXRvcikgewogICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3IuZGVzdHJveSgpCiAgICAgICAgICAgIHRoaXMucmljaEVkaXRvciA9IG51bGwKICAgICAgICAgICAgdGhpcy5lZGl0b3JJbml0aWFsaXplZCA9IGZhbHNlCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LAogICAgICBpbW1lZGlhdGU6IGZhbHNlCiAgICB9CiAgfSwKCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuaW5pdFBhZ2UoKQogICAgLy8g5Yib5bu66Ziy5oqW5Ye95pWwCiAgICB0aGlzLmRlYm91bmNlUGFyc2VEb2N1bWVudCA9IHRoaXMuZGVib3VuY2UodGhpcy5wYXJzZURvY3VtZW50LCAxMDAwKQogICAgLy8g5Yid5aeL5YyW5LiK5Lyg5pWw5o2uCiAgICB0aGlzLnVwbG9hZERhdGEgPSB7CiAgICAgIGJhbmtJZDogdGhpcy5iYW5rSWQKICAgIH0KICAgIHRoaXMudXBsb2FkSGVhZGVycyA9IHsKICAgICAgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgdGhpcy4kc3RvcmUuZ2V0dGVycy50b2tlbgogICAgfQogIH0sCgogIG1vdW50ZWQoKSB7CiAgICAvLyDnvJbovpHlmajlsIblnKjmir3lsYnmiZPlvIDml7bliJ3lp4vljJYKICAgIHRoaXMubG9hZENhY2hlZENvbnRlbnQoKQoKICAgIC8vIOebkeWQrOmhtemdouWFs+mXreS6i+S7tu+8jOS/neWtmOWGheWuuQogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIHRoaXMuaGFuZGxlQmVmb3JlVW5sb2FkKQogIH0sCgogIGJlZm9yZURlc3Ryb3koKSB7CiAgICAvLyDkv53lrZjlvZPliY3lhoXlrrnliLDnvJPlrZgKICAgIHRoaXMuc2F2ZVRvQ2FjaGVOb3coKQoKICAgIC8vIOa4heeQhuWumuaXtuWZqAogICAgaWYgKHRoaXMuYXV0b1NhdmVUaW1lcikgewogICAgICBjbGVhclRpbWVvdXQodGhpcy5hdXRvU2F2ZVRpbWVyKQogICAgfQoKICAgIC8vIOenu+mZpOS6i+S7tuebkeWQrOWZqAogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIHRoaXMuaGFuZGxlQmVmb3JlVW5sb2FkKQoKICAgIC8vIOmUgOavgeWvjOaWh+acrOe8lui+keWZqAogICAgaWYgKHRoaXMucmljaEVkaXRvcikgewogICAgICB0aGlzLnJpY2hFZGl0b3IuZGVzdHJveSgpCiAgICAgIHRoaXMucmljaEVkaXRvciA9IG51bGwKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWIneWni+WMlumhtemdogogICAgaW5pdFBhZ2UoKSB7CiAgICAgIGNvbnN0IHsgYmFua0lkLCBiYW5rTmFtZSB9ID0gdGhpcy4kcm91dGUucXVlcnkKICAgICAgaWYgKCFiYW5rSWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvLrlsJHpopjlupNJROWPguaVsCcpCiAgICAgICAgdGhpcy5nb0JhY2soKQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIHRoaXMuYmFua0lkID0gYmFua0lkCiAgICAgIHRoaXMuYmFua05hbWUgPSBiYW5rTmFtZSB8fCAn6aKY5bqT6K+m5oOFJwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmJhbmtJZCA9IGJhbmtJZAogICAgICB0aGlzLmdldFF1ZXN0aW9uTGlzdCgpCiAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpCiAgICB9LAogICAgLy8g6L+U5Zue6aKY5bqT5YiX6KGoCiAgICBnb0JhY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5iYWNrKCkKICAgIH0sCiAgICAvLyDojrflj5bpopjnm67liJfooagKICAgIGdldFF1ZXN0aW9uTGlzdCgpIHsKICAgICAgLy8g6L2s5o2i5p+l6K+i5Y+C5pWw5qC85byPCiAgICAgIGNvbnN0IHBhcmFtcyA9IHRoaXMuY29udmVydFF1ZXJ5UGFyYW1zKHRoaXMucXVlcnlQYXJhbXMpCiAgICAgIGxpc3RRdWVzdGlvbihwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMucXVlc3Rpb25MaXN0ID0gcmVzcG9uc2Uucm93cwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbAogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CgogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlumimOebruWIl+ihqOWksei0pScpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOi9rOaNouafpeivouWPguaVsOagvOW8jwogICAgY29udmVydFF1ZXJ5UGFyYW1zKHBhcmFtcykgewogICAgICBjb25zdCBjb252ZXJ0ZWRQYXJhbXMgPSB7IC4uLnBhcmFtcyB9CgogICAgICAvLyDovazmjaLpopjlnosKICAgICAgaWYgKGNvbnZlcnRlZFBhcmFtcy5xdWVzdGlvblR5cGUpIHsKICAgICAgICBjb25zdCB0eXBlTWFwID0gewogICAgICAgICAgJ3NpbmdsZSc6IDEsCiAgICAgICAgICAnbXVsdGlwbGUnOiAyLAogICAgICAgICAgJ2p1ZGdtZW50JzogMwogICAgICAgIH0KICAgICAgICBjb252ZXJ0ZWRQYXJhbXMucXVlc3Rpb25UeXBlID0gdHlwZU1hcFtjb252ZXJ0ZWRQYXJhbXMucXVlc3Rpb25UeXBlXSB8fCBjb252ZXJ0ZWRQYXJhbXMucXVlc3Rpb25UeXBlCiAgICAgIH0KCiAgICAgIC8vIOi9rOaNoumavuW6pgogICAgICBpZiAoY29udmVydGVkUGFyYW1zLmRpZmZpY3VsdHkpIHsKICAgICAgICBjb25zdCBkaWZmaWN1bHR5TWFwID0gewogICAgICAgICAgJ+eugOWNlSc6IDEsCiAgICAgICAgICAn5Lit562JJzogMiwKICAgICAgICAgICflm7Dpmr4nOiAzCiAgICAgICAgfQogICAgICAgIGNvbnZlcnRlZFBhcmFtcy5kaWZmaWN1bHR5ID0gZGlmZmljdWx0eU1hcFtjb252ZXJ0ZWRQYXJhbXMuZGlmZmljdWx0eV0gfHwgY29udmVydGVkUGFyYW1zLmRpZmZpY3VsdHkKICAgICAgfQoKICAgICAgLy8g5riF55CG56m65YC8CiAgICAgIE9iamVjdC5rZXlzKGNvbnZlcnRlZFBhcmFtcykuZm9yRWFjaChrZXkgPT4gewogICAgICAgIGlmIChjb252ZXJ0ZWRQYXJhbXNba2V5XSA9PT0gJycgfHwgY29udmVydGVkUGFyYW1zW2tleV0gPT09IG51bGwgfHwgY29udmVydGVkUGFyYW1zW2tleV0gPT09IHVuZGVmaW5lZCkgewogICAgICAgICAgZGVsZXRlIGNvbnZlcnRlZFBhcmFtc1trZXldCiAgICAgICAgfQogICAgICB9KQoKICAgICAgcmV0dXJuIGNvbnZlcnRlZFBhcmFtcwogICAgfSwKICAgIC8vIOiOt+WPlue7n+iuoeaVsOaNrgogICAgZ2V0U3RhdGlzdGljcygpIHsKICAgICAgZ2V0UXVlc3Rpb25TdGF0aXN0aWNzKHRoaXMuYmFua0lkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnN0YXRpc3RpY3MgPSByZXNwb25zZS5kYXRhCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKCiAgICAgICAgLy8g5L2/55So5qih5ouf5pWw5o2uCiAgICAgICAgdGhpcy5zdGF0aXN0aWNzID0gewogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaW5nbGVDaG9pY2U6IDAsCiAgICAgICAgICBtdWx0aXBsZUNob2ljZTogMCwKICAgICAgICAgIGp1ZGdtZW50OiAwCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8vIOaJuemHj+WvvOWFpQogICAgaGFuZGxlQmF0Y2hJbXBvcnQoKSB7CiAgICAgIHRoaXMuYmF0Y2hJbXBvcnRWaXNpYmxlID0gdHJ1ZQogICAgfSwKICAgIC8vIOa3u+WKoOmimOebrgogICAgaGFuZGxlQWRkUXVlc3Rpb24odHlwZSkgewogICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvblR5cGUgPSB0eXBlCiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uRGF0YSA9IG51bGwKICAgICAgdGhpcy5xdWVzdGlvbkZvcm1WaXNpYmxlID0gdHJ1ZQogICAgfSwKICAgIC8vIOWIh+aNouWxleW8gOeKtuaAgQogICAgdG9nZ2xlRXhwYW5kQWxsKCkgewogICAgICB0aGlzLmV4cGFuZEFsbCA9ICF0aGlzLmV4cGFuZEFsbAogICAgICBpZiAoIXRoaXMuZXhwYW5kQWxsKSB7CiAgICAgICAgdGhpcy5leHBhbmRlZFF1ZXN0aW9ucyA9IFtdCiAgICAgIH0KICAgIH0sCgoKCiAgICAvLyDlr7zlh7rpopjnm64KICAgIGhhbmRsZUV4cG9ydFF1ZXN0aW9ucygpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHlr7zlh7rnmoTpopjnm64nKQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbyhg5q2j5Zyo5a+85Ye6ICR7dGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGh9IOmBk+mimOebri4uLmApCiAgICAgIC8vIFRPRE86IOWunueOsOWvvOWHuuWKn+iDvQogICAgfSwKCiAgICAvLyDliIfmjaLlhajpgIkv5YWo5LiN6YCJCiAgICBoYW5kbGVUb2dnbGVTZWxlY3RBbGwoKSB7CiAgICAgIHRoaXMuaXNBbGxTZWxlY3RlZCA9ICF0aGlzLmlzQWxsU2VsZWN0ZWQKICAgICAgaWYgKHRoaXMuaXNBbGxTZWxlY3RlZCkgewogICAgICAgIC8vIOWFqOmAiQogICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMgPSB0aGlzLnF1ZXN0aW9uTGlzdC5tYXAocSA9PiBxLnF1ZXN0aW9uSWQpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlt7LpgInmi6kgJHt0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uuYCkKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlhajkuI3pgIkKICAgICAgICB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3suWPlua2iOmAieaLqeaJgOaciemimOebricpCiAgICAgIH0KICAgIH0sCgoKCiAgICAvLyDmibnph4/liKDpmaQKICAgIGhhbmRsZUJhdGNoRGVsZXRlKCkgewogICAgICBpZiAodGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeWIoOmZpOeahOmimOebricpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRoaXMuJGNvbmZpcm0oYOehruiupOWIoOmZpOmAieS4reeahCAke3RoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm67lkJfvvJ9gLCAn5om56YeP5Yig6Zmk56Gu6K6kJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgLy8g6L+Z6YeM5bqU6K+l6LCD55So5om56YeP5Yig6ZmkQVBJCiAgICAgICAgLy8g5pqC5pe25L2/55So5Y2V5Liq5Yig6Zmk55qE5pa55byPCiAgICAgICAgY29uc3QgZGVsZXRlUHJvbWlzZXMgPSB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLm1hcChxdWVzdGlvbklkID0+CiAgICAgICAgICBkZWxRdWVzdGlvbihxdWVzdGlvbklkKQogICAgICAgICkKCiAgICAgICAgUHJvbWlzZS5hbGwoZGVsZXRlUHJvbWlzZXMpLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/liKDpmaQgJHt0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uuYCkKICAgICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMgPSBbXQogICAgICAgICAgdGhpcy5hbGxTZWxlY3RlZCA9IGZhbHNlCiAgICAgICAgICB0aGlzLmdldFF1ZXN0aW9uTGlzdCgpCiAgICAgICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmibnph4/liKDpmaTlpLHotKUnKQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOaJuemHj+WIoOmZpAogICAgaGFuZGxlQmF0Y2hEZWxldGUoKSB7CiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB5Yig6Zmk55qE6aKY55uuJykKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdGhpcy4kY29uZmlybShg56Gu6K6k5Yig6Zmk6YCJ5Lit55qEICR7dGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGh9IOmBk+mimOebruWQl++8n2AsICfmibnph4/liKDpmaQnLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAvLyDmibnph4/liKDpmaRBUEnosIPnlKgKICAgICAgICBjb25zdCBkZWxldGVQcm9taXNlcyA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubWFwKHF1ZXN0aW9uSWQgPT4KICAgICAgICAgIGRlbFF1ZXN0aW9uKHF1ZXN0aW9uSWQpCiAgICAgICAgKQoKICAgICAgICBQcm9taXNlLmFsbChkZWxldGVQcm9taXNlcykudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaIkOWKn+WIoOmZpCAke3RoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm65gKQogICAgICAgICAgdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgICB0aGlzLmlzQWxsU2VsZWN0ZWQgPSBmYWxzZQogICAgICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CgogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5om56YeP5Yig6Zmk5aSx6LSlJykKICAgICAgICB9KQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7Llj5bmtojliKDpmaQnKQogICAgICB9KQogICAgfSwKCiAgICAvLyDpopjnm67pgInmi6nnirbmgIHlj5jljJYKICAgIGhhbmRsZVF1ZXN0aW9uU2VsZWN0KHF1ZXN0aW9uSWQsIHNlbGVjdGVkKSB7CiAgICAgIGlmIChzZWxlY3RlZCkgewogICAgICAgIGlmICghdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5pbmNsdWRlcyhxdWVzdGlvbklkKSkgewogICAgICAgICAgdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5wdXNoKHF1ZXN0aW9uSWQpCiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5pbmRleE9mKHF1ZXN0aW9uSWQpCiAgICAgICAgaWYgKGluZGV4ID4gLTEpIHsKICAgICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMuc3BsaWNlKGluZGV4LCAxKQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5pu05paw5YWo6YCJ54q25oCBCiAgICAgIHRoaXMuaXNBbGxTZWxlY3RlZCA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoID09PSB0aGlzLnF1ZXN0aW9uTGlzdC5sZW5ndGgKICAgIH0sCiAgICAvLyDliIfmjaLljZXkuKrpopjnm67lsZXlvIDnirbmgIEKICAgIGhhbmRsZVRvZ2dsZUV4cGFuZChxdWVzdGlvbklkKSB7CiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5leHBhbmRlZFF1ZXN0aW9ucy5pbmRleE9mKHF1ZXN0aW9uSWQpCiAgICAgIGlmIChpbmRleCA+IC0xKSB7CiAgICAgICAgdGhpcy5leHBhbmRlZFF1ZXN0aW9ucy5zcGxpY2UoaW5kZXgsIDEpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5leHBhbmRlZFF1ZXN0aW9ucy5wdXNoKHF1ZXN0aW9uSWQpCiAgICAgIH0KICAgIH0sCiAgICAvLyDnvJbovpHpopjnm64KICAgIGhhbmRsZUVkaXRRdWVzdGlvbihxdWVzdGlvbikgewogICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvbkRhdGEgPSBxdWVzdGlvbgogICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvblR5cGUgPSBxdWVzdGlvbi5xdWVzdGlvblR5cGUKICAgICAgdGhpcy5xdWVzdGlvbkZvcm1WaXNpYmxlID0gdHJ1ZQogICAgfSwKICAgIC8vIOWkjeWItumimOebrgogICAgaGFuZGxlQ29weVF1ZXN0aW9uKHF1ZXN0aW9uKSB7CiAgICAgIC8vIOWIm+W7uuWkjeWItueahOmimOebruaVsOaNru+8iOenu+mZpElE55u45YWz5a2X5q6177yJCiAgICAgIGNvbnN0IGNvcGllZFF1ZXN0aW9uID0gewogICAgICAgIC4uLnF1ZXN0aW9uLAogICAgICAgIHF1ZXN0aW9uSWQ6IG51bGwsICAvLyDmuIXpmaRJRO+8jOihqOekuuaWsOWingogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwKICAgICAgICBjcmVhdGVCeTogbnVsbCwKICAgICAgICB1cGRhdGVCeTogbnVsbAogICAgICB9CgogICAgICAvLyDorr7nva7kuLrnvJbovpHmqKHlvI/lubbmiZPlvIDooajljZUKICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb25EYXRhID0gY29waWVkUXVlc3Rpb24KICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb25UeXBlID0gdGhpcy5jb252ZXJ0UXVlc3Rpb25UeXBlVG9TdHJpbmcocXVlc3Rpb24ucXVlc3Rpb25UeXBlKQogICAgICB0aGlzLnF1ZXN0aW9uRm9ybVZpc2libGUgPSB0cnVlCiAgICB9LAoKICAgIC8vIOmimOWei+aVsOWtl+i9rOWtl+espuS4su+8iOeUqOS6juWkjeWItuWKn+iDve+8iQogICAgY29udmVydFF1ZXN0aW9uVHlwZVRvU3RyaW5nKHR5cGUpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAxOiAnc2luZ2xlJywKICAgICAgICAyOiAnbXVsdGlwbGUnLAogICAgICAgIDM6ICdqdWRnbWVudCcKICAgICAgfQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCB0eXBlCiAgICB9LAogICAgLy8g5Yig6Zmk6aKY55uuCiAgICBoYW5kbGVEZWxldGVRdWVzdGlvbihxdWVzdGlvbikgewogICAgICBjb25zdCBxdWVzdGlvbkNvbnRlbnQgPSBxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQucmVwbGFjZSgvPFtePl0qPi9nLCAnJykKICAgICAgY29uc3QgZGlzcGxheUNvbnRlbnQgPSBxdWVzdGlvbkNvbnRlbnQubGVuZ3RoID4gNTAgPyBxdWVzdGlvbkNvbnRlbnQuc3Vic3RyaW5nKDAsIDUwKSArICcuLi4nIDogcXVlc3Rpb25Db250ZW50CiAgICAgIHRoaXMuJGNvbmZpcm0oYOehruiupOWIoOmZpOmimOebriIke2Rpc3BsYXlDb250ZW50fSLlkJfvvJ9gLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgZGVsUXVlc3Rpb24ocXVlc3Rpb24ucXVlc3Rpb25JZCkudGhlbigoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpCiAgICAgICAgICB0aGlzLmdldFF1ZXN0aW9uTGlzdCgpCiAgICAgICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliKDpmaTpopjnm67lpLHotKUnKQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgLy8g6aKY55uu6KGo5Y2V5oiQ5Yqf5Zue6LCDCiAgICBoYW5kbGVRdWVzdGlvbkZvcm1TdWNjZXNzKCkgewogICAgICB0aGlzLnF1ZXN0aW9uRm9ybVZpc2libGUgPSBmYWxzZQogICAgICB0aGlzLmdldFF1ZXN0aW9uTGlzdCgpCiAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpCiAgICB9LAogICAgLy8g5om56YeP5a+85YWl5oiQ5Yqf5Zue6LCDCiAgICBoYW5kbGVCYXRjaEltcG9ydFN1Y2Nlc3MoKSB7CiAgICAgIHRoaXMuYmF0Y2hJbXBvcnRWaXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy5pbXBvcnREcmF3ZXJWaXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKQogICAgfSwKCgoKICAgIC8vIOaKveWxieWFs+mXreWJjeWkhOeQhgogICAgaGFuZGxlRHJhd2VyQ2xvc2UoZG9uZSkgewogICAgICBkb25lKCkKICAgIH0sCgogICAgLy8g5pi+56S65paH5qGj5a+85YWl5a+56K+d5qGGCiAgICBzaG93RG9jdW1lbnRJbXBvcnREaWFsb2coKSB7CiAgICAgIC8vIOa4hemZpOS4iuS4gOasoeeahOS4iuS8oOeKtuaAgeWSjOWGheWuuQogICAgICB0aGlzLmlzVXBsb2FkaW5nID0gZmFsc2UKICAgICAgdGhpcy5pc1BhcnNpbmcgPSBmYWxzZQoKICAgICAgLy8g5riF6Zmk5LiK5Lyg57uE5Lu255qE5paH5Lu25YiX6KGoCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICBjb25zdCB1cGxvYWRDb21wb25lbnQgPSB0aGlzLiRyZWZzLmRvY3VtZW50VXBsb2FkCiAgICAgICAgaWYgKHVwbG9hZENvbXBvbmVudCkgewogICAgICAgICAgdXBsb2FkQ29tcG9uZW50LmNsZWFyRmlsZXMoKQogICAgICAgIH0KICAgICAgfSkKCiAgICAgIHRoaXMuZG9jdW1lbnRJbXBvcnREaWFsb2dWaXNpYmxlID0gdHJ1ZQoKICAgIH0sCgogICAgLy8g5pi+56S66KeE6IyD5a+56K+d5qGGCiAgICBzaG93UnVsZXNEaWFsb2coKSB7CiAgICAgIHRoaXMuYWN0aXZlUnVsZVRhYiA9ICdleGFtcGxlcycgLy8g6buY6K6k5pi+56S66IyD5L6L5qCH562+6aG1CiAgICAgIHRoaXMucnVsZXNEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgfSwKCiAgICAvLyDlsIbojIPkvovlpI3liLbliLDnvJbovpHljLogLSDlj6rkv53nlZnliY0z6aKY77ya5Y2V6YCJ44CB5aSa6YCJ44CB5Yik5patCiAgICBjb3B5RXhhbXBsZVRvRWRpdG9yKCkgewogICAgICAvLyDkvb/nlKjovpPlhaXojIPkvovmoIfnrb7pobXph4znmoTliY0z6aKY5YaF5a6577yM6L2s5o2i5Li6SFRNTOagvOW8jwogICAgICBjb25zdCBodG1sVGVtcGxhdGUgPSBgCjxwPjEu77yIICDvvInmmK/miJHlm73mnIDml6nnmoTor5fmrYzmgLvpm4bvvIzlj4jnp7DkvZwi6K+X5LiJ55m+IuOAgjwvcD4KPHA+QS7jgIrlt6bkvKDjgIs8L3A+CjxwPkIu44CK56a76aqa44CLPC9wPgo8cD5DLuOAiuWdm+e7j+OAizwvcD4KPHA+RC7jgIror5fnu4/jgIs8L3A+CjxwPuetlOahiO+8mkQ8L3A+CjxwPuino+aekO+8muivl+e7j+aYr+aIkeWbveacgOaXqeeahOivl+atjOaAu+mbhuOAgjwvcD4KPHA+6Zq+5bqm77ya5Lit562JPC9wPgo8cD48YnI+PC9wPgoKPHA+Mi7kuK3ljY7kurrmsJHlhbHlkozlm73nmoTmiJDnq4vvvIzmoIflv5fnnYDvvIgg77yJ44CCPC9wPgo8cD5BLuS4reWbveaWsOawkeS4u+S4u+S5iemdqeWRveWPluW+l+S6huWfuuacrOiDnOWIqTwvcD4KPHA+Qi7kuK3lm73njrDku6Plj7LnmoTlvIDlp4s8L3A+CjxwPkMu5Y2K5q6W5rCR5Zyw5Y2K5bCB5bu656S+5Lya55qE57uT5p2fPC9wPgo8cD5ELuS4reWbvei/m+WFpeekvuS8muS4u+S5ieekvuS8mjwvcD4KPHA+562U5qGI77yaQUJDPC9wPgo8cD7op6PmnpDvvJrmlrDkuK3lm73nmoTmiJDnq4vvvIzmoIflv5fnnYDmiJHlm73mlrDmsJHkuLvkuLvkuYnpnanlkb3pmLbmrrXnmoTln7rmnKznu5PmnZ/lkoznpL7kvJrkuLvkuYnpnanlkb3pmLbmrrXnmoTlvIDlp4vjgII8L3A+CjxwPjxicj48L3A+Cgo8cD4zLuWFg+adguWJp+eahOWbm+Wkp+aCsuWJp+aYr++8muWFs+axieWNv+eahOOAiueqpuWopeWGpOOAi++8jOmprOiHtOi/nOeahOOAiuaxieWuq+eni+OAi++8jOeZveactOeahOOAiuaip+ahkOmbqOOAi+WSjOmDkeWFieellueahOOAiui1teawj+WtpOWEv+OAi+OAgjwvcD4KPHA+562U5qGI77ya6ZSZ6K+vPC9wPgo8cD7op6PmnpDvvJrlhYPmnYLliafjgIrotbXmsI/lraTlhL/jgIvlhajlkI3jgIrlhqTmiqXlhqTotbXmsI/lraTlhL/jgIvvvIzkuLrnuqrlkJvnpaXmiYDkvZzjgII8L3A+CiAgICAgIGAudHJpbSgpCgogICAgICAvLyDnm7TmjqXorr7nva7liLDlr4zmlofmnKznvJbovpHlmagKICAgICAgaWYgKHRoaXMucmljaEVkaXRvciAmJiB0aGlzLmVkaXRvckluaXRpYWxpemVkKSB7CiAgICAgICAgdGhpcy5yaWNoRWRpdG9yLnNldERhdGEoaHRtbFRlbXBsYXRlKQoKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlpoLmnpznvJbovpHlmajmnKrliJ3lp4vljJbvvIznrYnlvoXliJ3lp4vljJblkI7lho3orr7nva4KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICBpZiAodGhpcy5yaWNoRWRpdG9yICYmIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQpIHsKICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLnNldERhdGEoaHRtbFRlbXBsYXRlKQoKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9CgogICAgICAvLyDlhbPpl63lr7nor53moYYKICAgICAgdGhpcy5ydWxlc0RpYWxvZ1Zpc2libGUgPSBmYWxzZQoKICAgICAgLy8g5o+Q56S655So5oi3CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6L6T5YWl6IyD5L6L5bey5aGr5YWF5Yiw57yW6L6R5Yy677yM5Y+z5L6n5bCG6Ieq5Yqo6Kej5p6QJykKCgogICAgfSwKCiAgICAvLyDkuIvovb1FeGNlbOaooeadvwogICAgZG93bmxvYWRFeGNlbFRlbXBsYXRlKCkgewogICAgICB0aGlzLmRvd25sb2FkKCdiaXovcXVlc3Rpb25CYW5rL2Rvd25sb2FkRXhjZWxUZW1wbGF0ZScsIHt9LCBg6aKY55uu5a+85YWlRXhjZWzmqKHmnb8ueGxzeGApCiAgICB9LAoKICAgIC8vIOS4i+i9vVdvcmTmqKHmnb8KICAgIGRvd25sb2FkV29yZFRlbXBsYXRlKCkgewogICAgICB0aGlzLmRvd25sb2FkKCdiaXovcXVlc3Rpb25CYW5rL2Rvd25sb2FkV29yZFRlbXBsYXRlJywge30sIGDpopjnm67lr7zlhaVXb3Jk5qih5p2/LmRvY3hgKQogICAgfSwKCiAgICAvLyDkuIrkvKDliY3mo4Dmn6UKICAgIGJlZm9yZVVwbG9hZChmaWxlKSB7CgoKICAgICAgY29uc3QgaXNWYWxpZFR5cGUgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCcgfHwKICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyB8fAogICAgICAgICAgICAgICAgICAgICAgICAgZmlsZS5uYW1lLmVuZHNXaXRoKCcuZG9jeCcpIHx8IGZpbGUubmFtZS5lbmRzV2l0aCgnLnhsc3gnKQogICAgICBjb25zdCBpc0x0MTBNID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCAxMAoKICAgICAgaWYgKCFpc1ZhbGlkVHlwZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oCAuZG9jeCDmiJYgLnhsc3gg5qC85byP55qE5paH5Lu2IScpCiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0KICAgICAgaWYgKCFpc0x0MTBNKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDEwTUIhJykKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQoKICAgICAgLy8g5pu05paw5LiK5Lyg5pWw5o2uCiAgICAgIHRoaXMudXBsb2FkRGF0YS5iYW5rSWQgPSB0aGlzLmJhbmtJZAoKICAgICAgLy8g6K6+572u5LiK5Lyg54q25oCBCiAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSB0cnVlCiAgICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2UKCgoKICAgICAgcmV0dXJuIHRydWUKICAgIH0sCgogICAgLy8g5LiK5Lyg5oiQ5YqfCiAgICBoYW5kbGVVcGxvYWRTdWNjZXNzKHJlc3BvbnNlLCBmaWxlKSB7CgoKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgIC8vIOS4iuS8oOWujOaIkO+8jOW8gOWni+ino+aekAogICAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZQogICAgICAgIHRoaXMuaXNQYXJzaW5nID0gdHJ1ZQoKCgogICAgICAgIC8vIOa4hemZpOS5i+WJjeeahOino+aekOe7k+aenO+8jOehruS/neW5suWHgOeahOW8gOWniwogICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KCiAgICAgICAgLy8g5bu26L+f5YWz6Zet5a+56K+d5qGG77yM6K6p55So5oi355yL5Yiw6Kej5p6Q5Yqo55S7CiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICB0aGlzLmRvY3VtZW50SW1wb3J0RGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgICB0aGlzLmlzUGFyc2luZyA9IGZhbHNlCiAgICAgICAgfSwgMTUwMCkKCiAgICAgICAgLy8g6K6+572u5qCH5b+X5L2N77yM6YG/5YWN6Kem5Y+R5YmN56uv6YeN5paw6Kej5p6QCiAgICAgICAgdGhpcy5pc1NldHRpbmdGcm9tQmFja2VuZCA9IHRydWUKCiAgICAgICAgLy8g5bCG6Kej5p6Q57uT5p6c5pi+56S65Zyo5Y+z5L6nCiAgICAgICAgaWYgKHJlc3BvbnNlLnF1ZXN0aW9ucyAmJiByZXNwb25zZS5xdWVzdGlvbnMubGVuZ3RoID4gMCkgewogICAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSByZXNwb25zZS5xdWVzdGlvbnMubWFwKHF1ZXN0aW9uID0+ICh7CiAgICAgICAgICAgIC4uLnF1ZXN0aW9uLAogICAgICAgICAgICBjb2xsYXBzZWQ6IGZhbHNlICAvLyDpu5jorqTlsZXlvIAKICAgICAgICAgIH0pKQogICAgICAgICAgLy8g6YeN572u5YWo6YOo5bGV5byA54q25oCBCiAgICAgICAgICB0aGlzLmFsbEV4cGFuZGVkID0gdHJ1ZQogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHJlc3BvbnNlLmVycm9ycyB8fCBbXQoKICAgICAgICAgIC8vIOaYvuekuuivpue7hueahOino+aekOe7k+aenAogICAgICAgICAgY29uc3QgZXJyb3JDb3VudCA9IHJlc3BvbnNlLmVycm9ycyA/IHJlc3BvbnNlLmVycm9ycy5sZW5ndGggOiAwCiAgICAgICAgICBpZiAoZXJyb3JDb3VudCA+IDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/op6PmnpDlh7ogJHtyZXNwb25zZS5xdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm67vvIzmnIkgJHtlcnJvckNvdW50fSDkuKrplJnor6/miJborablkYpgKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/op6PmnpDlh7ogJHtyZXNwb25zZS5xdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm65gKQogICAgICAgICAgfQoKCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+acquino+aekOWHuuS7u+S9lemimOebru+8jOivt+ajgOafpeaWh+S7tuagvOW8jycpCiAgICAgICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gcmVzcG9uc2UuZXJyb3JzIHx8IFsn5pyq6IO96Kej5p6Q5Ye66aKY55uu5YaF5a65J10KCgogICAgICAgIH0KCiAgICAgICAgLy8g5bCG5Y6f5aeL5YaF5a655aGr5YWF5Yiw5a+M5paH5pys57yW6L6R5Zmo5LitCiAgICAgICAgaWYgKHJlc3BvbnNlLm9yaWdpbmFsQ29udGVudCkgewogICAgICAgICAgdGhpcy5zZXRFZGl0b3JDb250ZW50KHJlc3BvbnNlLm9yaWdpbmFsQ29udGVudCkKICAgICAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gcmVzcG9uc2Uub3JpZ2luYWxDb250ZW50CiAgICAgICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSByZXNwb25zZS5vcmlnaW5hbENvbnRlbnQgLy8g5Yid5aeL5YyWSFRNTOWGheWuuQogICAgICAgICAgdGhpcy5sYXN0U2F2ZVRpbWUgPSBuZXcgRGF0ZSgpLnRvTG9jYWxlU3RyaW5nKCkKICAgICAgICB9CgogICAgICAgIC8vIOW7tui/n+mHjee9ruagh+W/l+S9je+8jOehruS/neaJgOacieW8guatpeaTjeS9nOWujOaIkAogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgdGhpcy5pc1NldHRpbmdGcm9tQmFja2VuZCA9IGZhbHNlCiAgICAgICAgfSwgMjAwMCkKICAgICAgfSBlbHNlIHsKCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+aWh+S7tuS4iuS8oOWksei0pScpCiAgICAgICAgLy8g6YeN572u54q25oCBCiAgICAgICAgdGhpcy5pc1VwbG9hZGluZyA9IGZhbHNlCiAgICAgICAgdGhpcy5pc1BhcnNpbmcgPSBmYWxzZQogICAgICB9CiAgICB9LAoKICAgIC8vIOS4iuS8oOWksei0pQogICAgaGFuZGxlVXBsb2FkRXJyb3IoZXJyb3IsIGZpbGUpIHsKCiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuS4iuS8oOWksei0pe+8jOivt+ajgOafpee9kee7nOi/nuaOpeaIluiBlOezu+euoeeQhuWRmCcpCgogICAgICAvLyDph43nva7nirbmgIEKICAgICAgdGhpcy5pc1VwbG9hZGluZyA9IGZhbHNlCiAgICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2UKICAgIH0sCgogICAgLy8g5YWo6YOo5pS26LW3CiAgICBjb2xsYXBzZUFsbCgpIHsKICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMuZm9yRWFjaChxdWVzdGlvbiA9PiB7CiAgICAgICAgdGhpcy4kc2V0KHF1ZXN0aW9uLCAnY29sbGFwc2VkJywgdHJ1ZSkKICAgICAgfSkKICAgIH0sCgogICAgLy8g5YiH5o2i6aKY55uu5bGV5byAL+aUtui1twogICAgdG9nZ2xlUXVlc3Rpb24oaW5kZXgpIHsKICAgICAgY29uc3QgcXVlc3Rpb24gPSB0aGlzLnBhcnNlZFF1ZXN0aW9uc1tpbmRleF0KICAgICAgdGhpcy4kc2V0KHF1ZXN0aW9uLCAnY29sbGFwc2VkJywgIXF1ZXN0aW9uLmNvbGxhcHNlZCkKICAgIH0sCgogICAgLy8g5YWo6YOo5bGV5byAL+aUtui1twogICAgdG9nZ2xlQWxsUXVlc3Rpb25zKCkgewogICAgICB0aGlzLmFsbEV4cGFuZGVkID0gIXRoaXMuYWxsRXhwYW5kZWQKICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMuZm9yRWFjaChxdWVzdGlvbiA9PiB7CiAgICAgICAgdGhpcy4kc2V0KHF1ZXN0aW9uLCAnY29sbGFwc2VkJywgIXRoaXMuYWxsRXhwYW5kZWQpCiAgICAgIH0pCgogICAgfSwKCiAgICAvLyDnoa7orqTlr7zlhaUKICAgIGNvbmZpcm1JbXBvcnQoKSB7CiAgICAgIGlmICh0aGlzLnBhcnNlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ayoeacieWPr+WvvOWFpeeahOmimOebricpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRoaXMuJGNvbmZpcm0oYOehruiupOWvvOWFpSAke3RoaXMucGFyc2VkUXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uu5ZCX77yfYCwgJ+ehruiupOWvvOWFpScsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ2luZm8nCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuaW1wb3J0UXVlc3Rpb25zKCkKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pCiAgICB9LAoKICAgIC8vIOWvvOWFpemimOebrgogICAgYXN5bmMgaW1wb3J0UXVlc3Rpb25zKCkgewogICAgICB0cnkgewogICAgICAgIC8vIOWkhOeQhuWvvOWFpemAiemhuQogICAgICAgIGxldCBxdWVzdGlvbnNUb0ltcG9ydCA9IFsuLi50aGlzLnBhcnNlZFF1ZXN0aW9uc10KCiAgICAgICAgaWYgKHRoaXMuaW1wb3J0T3B0aW9ucy5yZXZlcnNlKSB7CiAgICAgICAgICBxdWVzdGlvbnNUb0ltcG9ydC5yZXZlcnNlKCkKICAgICAgICB9CgogICAgICAgIC8vIOiwg+eUqOWunumZheeahOWvvOWFpUFQSQogICAgICAgIGNvbnN0IGltcG9ydERhdGEgPSB7CiAgICAgICAgICBiYW5rSWQ6IHRoaXMuYmFua0lkLAogICAgICAgICAgcXVlc3Rpb25zOiBxdWVzdGlvbnNUb0ltcG9ydCwKICAgICAgICAgIGFsbG93RHVwbGljYXRlOiB0aGlzLmltcG9ydE9wdGlvbnMuYWxsb3dEdXBsaWNhdGUKICAgICAgICB9CgogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYmF0Y2hJbXBvcnRRdWVzdGlvbnMoaW1wb3J0RGF0YSkKCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/lr7zlhaUgJHtxdWVzdGlvbnNUb0ltcG9ydC5sZW5ndGh9IOmBk+mimOebrmApCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tc2cgfHwgJ+WvvOWFpeWksei0pScpCiAgICAgICAgfQogICAgICAgIHRoaXMuaW1wb3J0RHJhd2VyVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgdGhpcy5kb2N1bWVudENvbnRlbnQgPSAnJwogICAgICAgIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCA9ICcnCiAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBbXQogICAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSBbXQoKICAgICAgICAvLyDlr7zlhaXmiJDlip/lkI7muIXpmaTnvJPlrZgKICAgICAgICB0aGlzLmNsZWFyQ2FjaGUoKQoKICAgICAgICB0aGlzLmdldFF1ZXN0aW9uTGlzdCgpCiAgICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5a+85YWl5aSx6LSlJykKICAgICAgfQogICAgfSwKCiAgICAvLyDliJ3lp4vljJblr4zmlofmnKznvJbovpHlmagKICAgIGluaXRSaWNoRWRpdG9yKCkgewogICAgICBpZiAodGhpcy5lZGl0b3JJbml0aWFsaXplZCkgewogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDmo4Dmn6VDS0VkaXRvcuaYr+WQpuWPr+eUqAogICAgICBpZiAoIXdpbmRvdy5DS0VESVRPUikgewoKICAgICAgICB0aGlzLmZhbGxiYWNrVG9UZXh0YXJlYSgpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8g5aaC5p6c57yW6L6R5Zmo5bey5a2Y5Zyo77yM5YWI6ZSA5q+BCiAgICAgICAgaWYgKHRoaXMucmljaEVkaXRvcikgewogICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLmRlc3Ryb3koKQogICAgICAgICAgdGhpcy5yaWNoRWRpdG9yID0gbnVsbAogICAgICAgIH0KCiAgICAgICAgLy8g56Gu5L+d5a655Zmo5a2Y5ZyoCiAgICAgICAgY29uc3QgZWRpdG9yQ29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3JpY2gtZWRpdG9yJykKICAgICAgICBpZiAoIWVkaXRvckNvbnRhaW5lcikgewoKICAgICAgICAgIHJldHVybgogICAgICAgIH0KCiAgICAgICAgLy8g5Yib5bu6dGV4dGFyZWHlhYPntKAKICAgICAgICBlZGl0b3JDb250YWluZXIuaW5uZXJIVE1MID0gJzx0ZXh0YXJlYSBpZD0icmljaC1lZGl0b3ItdGV4dGFyZWEiIG5hbWU9InJpY2gtZWRpdG9yLXRleHRhcmVhIj48L3RleHRhcmVhPicKCiAgICAgICAgLy8g562J5b6FRE9N5pu05paw5ZCO5Yib5bu657yW6L6R5ZmoCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgLy8g5qOA5p+lQ0tFZGl0b3LmmK/lkKblj6/nlKgKICAgICAgICAgIGlmICghd2luZG93LkNLRURJVE9SIHx8ICF3aW5kb3cuQ0tFRElUT1IucmVwbGFjZSkgewoKICAgICAgICAgICAgdGhpcy5zaG93RmFsbGJhY2tFZGl0b3IgPSB0cnVlCiAgICAgICAgICAgIHJldHVybgogICAgICAgICAgfQoKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIC8vIOWFiOWwneivleWujOaVtOmFjee9rgogICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3IgPSB3aW5kb3cuQ0tFRElUT1IucmVwbGFjZSgncmljaC1lZGl0b3ItdGV4dGFyZWEnLCB7CiAgICAgICAgICAgICAgaGVpZ2h0OiAnY2FsYygxMDB2aCAtIDIwMHB4KScsIC8vIOWFqOWxj+mrmOW6puWHj+WOu+WktOmDqOWSjOWFtuS7luWFg+e0oOeahOmrmOW6pgogICAgICAgICAgICAgIHRvb2xiYXI6IFsKICAgICAgICAgICAgICAgIHsgbmFtZTogJ3N0eWxlcycsIGl0ZW1zOiBbJ0ZvbnRTaXplJ10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ2Jhc2ljc3R5bGVzJywgaXRlbXM6IFsnQm9sZCcsICdJdGFsaWMnLCAnVW5kZXJsaW5lJywgJ1N0cmlrZScsICdTdXBlcnNjcmlwdCcsICdTdWJzY3JpcHQnLCAnLScsICdSZW1vdmVGb3JtYXQnXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAnY2xpcGJvYXJkJywgaXRlbXM6IFsnQ3V0JywgJ0NvcHknLCAnUGFzdGUnLCAnUGFzdGVUZXh0J10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ2NvbG9ycycsIGl0ZW1zOiBbJ1RleHRDb2xvcicsICdCR0NvbG9yJ10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ3BhcmFncmFwaCcsIGl0ZW1zOiBbJ0p1c3RpZnlMZWZ0JywgJ0p1c3RpZnlDZW50ZXInLCAnSnVzdGlmeVJpZ2h0JywgJ0p1c3RpZnlCbG9jayddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdlZGl0aW5nJywgaXRlbXM6IFsnVW5kbycsICdSZWRvJ10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ2xpbmtzJywgaXRlbXM6IFsnTGluaycsICdVbmxpbmsnXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAnaW5zZXJ0JywgaXRlbXM6IFsnSW1hZ2UnLCAnU3BlY2lhbENoYXInXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAndG9vbHMnLCBpdGVtczogWydNYXhpbWl6ZSddIH0KICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgIHJlbW92ZUJ1dHRvbnM6ICcnLAogICAgICAgICAgICAgIGxhbmd1YWdlOiAnemgtY24nLAogICAgICAgICAgICAgIHJlbW92ZVBsdWdpbnM6ICdlbGVtZW50c3BhdGgnLAogICAgICAgICAgICAgIHJlc2l6ZV9lbmFibGVkOiBmYWxzZSwKICAgICAgICAgICAgICBleHRyYVBsdWdpbnM6ICdmb250LGNvbG9yYnV0dG9uLGp1c3RpZnksc3BlY2lhbGNoYXIsaW1hZ2UnLAogICAgICAgICAgICAgIGFsbG93ZWRDb250ZW50OiB0cnVlLAogICAgICAgICAgICAgIC8vIOWtl+S9k+Wkp+Wwj+mFjee9rgogICAgICAgICAgICAgIGZvbnRTaXplX3NpemVzOiAnMTIvMTJweDsxNC8xNHB4OzE2LzE2cHg7MTgvMThweDsyMC8yMHB4OzIyLzIycHg7MjQvMjRweDsyNi8yNnB4OzI4LzI4cHg7MzYvMzZweDs0OC80OHB4OzcyLzcycHgnLAogICAgICAgICAgICAgIGZvbnRTaXplX2RlZmF1bHRMYWJlbDogJzE0cHgnLAogICAgICAgICAgICAgIC8vIOminOiJsumFjee9rgogICAgICAgICAgICAgIGNvbG9yQnV0dG9uX2VuYWJsZU1vcmU6IHRydWUsCiAgICAgICAgICAgICAgY29sb3JCdXR0b25fY29sb3JzOiAnQ0Y1RDRFLDQ1NDU0NSxGRkYsQ0NDLERERCxDQ0VBRUUsNjZBQjE2JywKICAgICAgICAgICAgICAvLyDlm77lg4/kuIrkvKDphY3nva4gLSDlj4LogIPmgqjmj5DkvpvnmoTmoIflh4bphY3nva4KICAgICAgICAgICAgICBmaWxlYnJvd3NlclVwbG9hZFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvY29tbW9uL3VwbG9hZEltYWdlJywKICAgICAgICAgICAgICBpbWFnZV9wcmV2aWV3VGV4dDogJyAnLAogICAgICAgICAgICAgIC8vIOiuvue9ruWfuuehgOi3r+W+hO+8jOiuqeebuOWvuei3r+W+hOiDveato+ehruino+aekOWIsOWQjuerr+acjeWKoeWZqAogICAgICAgICAgICAgIGJhc2VIcmVmOiAnaHR0cDovL2xvY2FsaG9zdDo4ODAyLycsCiAgICAgICAgICAgICAgLy8g5Zu+5YOP5o+S5YWl6YWN572uCiAgICAgICAgICAgICAgaW1hZ2VfcHJldmlld1RleHQ6ICfpooTop4jljLrln58nLAogICAgICAgICAgICAgIGltYWdlX3JlbW92ZUxpbmtCeUVtcHR5VVJMOiB0cnVlLAogICAgICAgICAgICAgIC8vIOmakOiXj+S4jemcgOimgeeahOagh+etvumhte+8jOWPquS/neeVmeS4iuS8oOWSjOWbvuWDj+S/oeaBrwogICAgICAgICAgICAgIHJlbW92ZURpYWxvZ1RhYnM6ICdpbWFnZTpMaW5rO2ltYWdlOmFkdmFuY2VkJywKICAgICAgICAgICAgICAvLyDplJnor6/lpITnkIblkozkuovku7bnm5HlkKwKICAgICAgICAgICAgICBvbjogewogICAgICAgICAgICAgICAgcGx1Z2luc0xvYWRlZDogZnVuY3Rpb24oKSB7CgogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIGluc3RhbmNlUmVhZHk6IGZ1bmN0aW9uKCkgewoKCiAgICAgICAgICAgICAgICAgIGNvbnN0IGVkaXRvciA9IGV2dC5lZGl0b3IKCiAgICAgICAgICAgICAgICAgIC8vIOeugOWNleeahOWvueivneahhuWkhOeQhiAtIOWPguiAg+aCqOaPkOS+m+eahOS7o+eggemjjuagvAogICAgICAgICAgICAgICAgICBlZGl0b3Iub24oJ2RpYWxvZ1Nob3cnLCBmdW5jdGlvbihldnQpIHsKICAgICAgICAgICAgICAgICAgICBjb25zdCBkaWFsb2cgPSBldnQuZGF0YQogICAgICAgICAgICAgICAgICAgIGlmIChkaWFsb2cuZ2V0TmFtZSgpID09PSAnaW1hZ2UnKSB7CgoKICAgICAgICAgICAgICAgICAgICAgIC8vIOeugOWNleajgOafpeS4iuS8oOWujOaIkOW5tuWIh+aNouagh+etvumhtQogICAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNoZWNrSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHVybEZpZWxkID0gZGlhbG9nLmdldENvbnRlbnRFbGVtZW50KCdpbmZvJywgJ3R4dFVybCcpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodXJsRmllbGQgJiYgdXJsRmllbGQuZ2V0VmFsdWUoKSAmJiB1cmxGaWVsZC5nZXRWYWx1ZSgpLnN0YXJ0c1dpdGgoJy8nKSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGVhckludGVydmFsKGNoZWNrSW50ZXJ2YWwpCgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDliIfmjaLliLDlm77lg4/kv6Hmga/moIfnrb7pobUKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlhbG9nLnNlbGVjdFBhZ2UoJ2luZm8nKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOW/veeVpemUmeivrwogICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgfSwgNTAwKQoKICAgICAgICAgICAgICAgICAgICAgICAgLy8gMTDnp5LlkI7lgZzmraLmo4Dmn6UKICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiBjbGVhckludGVydmFsKGNoZWNrSW50ZXJ2YWwpLCAxMDAwMCkKICAgICAgICAgICAgICAgICAgICAgIH0sIDEwMDApCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgfSwKCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKCgogICAgICAgICAgICAvLyDlsJ3or5XnroDljJbphY3nva4KICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3IgPSB3aW5kb3cuQ0tFRElUT1IucmVwbGFjZSgncmljaC1lZGl0b3ItdGV4dGFyZWEnLCB7CiAgICAgICAgICAgICAgICBoZWlnaHQ6ICdjYWxjKDEwMHZoIC0gMjAwcHgpJywKICAgICAgICAgICAgICAgIHRvb2xiYXI6IFsKICAgICAgICAgICAgICAgICAgWydCb2xkJywgJ0l0YWxpYycsICdVbmRlcmxpbmUnLCAnU3RyaWtlJ10sCiAgICAgICAgICAgICAgICAgIFsnTnVtYmVyZWRMaXN0JywgJ0J1bGxldGVkTGlzdCddLAogICAgICAgICAgICAgICAgICBbJ091dGRlbnQnLCAnSW5kZW50J10sCiAgICAgICAgICAgICAgICAgIFsnVW5kbycsICdSZWRvJ10sCiAgICAgICAgICAgICAgICAgIFsnTGluaycsICdVbmxpbmsnXSwKICAgICAgICAgICAgICAgICAgWydJbWFnZScsICdSZW1vdmVGb3JtYXQnLCAnTWF4aW1pemUnXQogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIHJlbW92ZUJ1dHRvbnM6ICcnLAogICAgICAgICAgICAgICAgbGFuZ3VhZ2U6ICd6aC1jbicsCiAgICAgICAgICAgICAgICByZW1vdmVQbHVnaW5zOiAnZWxlbWVudHNwYXRoJywKICAgICAgICAgICAgICAgIHJlc2l6ZV9lbmFibGVkOiBmYWxzZSwKICAgICAgICAgICAgICAgIGV4dHJhUGx1Z2luczogJ2ltYWdlJywKICAgICAgICAgICAgICAgIGFsbG93ZWRDb250ZW50OiB0cnVlLAogICAgICAgICAgICAgICAgLy8g5Zu+5YOP5LiK5Lyg6YWN572uIC0g5Y+C6ICD5oKo5o+Q5L6b55qE5qCH5YeG6YWN572uCiAgICAgICAgICAgICAgICBmaWxlYnJvd3NlclVwbG9hZFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvY29tbW9uL3VwbG9hZEltYWdlJywKICAgICAgICAgICAgICAgIGltYWdlX3ByZXZpZXdUZXh0OiAnICcsCiAgICAgICAgICAgICAgICAvLyDorr7nva7ln7rnoYDot6/lvoTvvIzorqnnm7jlr7not6/lvoTog73mraPnoa7op6PmnpDliLDlkI7nq6/mnI3liqHlmagKICAgICAgICAgICAgICAgIGJhc2VIcmVmOiAnaHR0cDovL2xvY2FsaG9zdDo4ODAyLycsCiAgICAgICAgICAgICAgICAvLyDpmpDol4/kuI3pnIDopoHnmoTmoIfnrb7pobXvvIzlj6rkv53nlZnkuIrkvKDlkozlm77lg4/kv6Hmga8KICAgICAgICAgICAgICAgIHJlbW92ZURpYWxvZ1RhYnM6ICdpbWFnZTpMaW5rO2ltYWdlOmFkdmFuY2VkJywKICAgICAgICAgICAgICAgIC8vIOa3u+WKoOWunuS+i+Wwsee7quS6i+S7tuWkhOeQhgogICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgaW5zdGFuY2VSZWFkeTogZnVuY3Rpb24oZXZ0KSB7CgoKICAgICAgICAgICAgICAgICAgICBjb25zdCBlZGl0b3IgPSBldnQuZWRpdG9yCgogICAgICAgICAgICAgICAgICAgIC8vIOebkeWQrOWvueivneahhuaYvuekuuS6i+S7tgogICAgICAgICAgICAgICAgICAgIGVkaXRvci5vbignZGlhbG9nU2hvdycsIGZ1bmN0aW9uKGV2dCkgewogICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGlhbG9nID0gZXZ0LmRhdGEKICAgICAgICAgICAgICAgICAgICAgIGlmIChkaWFsb2cuZ2V0TmFtZSgpID09PSAnaW1hZ2UnKSB7CgoKICAgICAgICAgICAgICAgICAgICAgICAgLy8g566A5Y2V5qOA5p+l5LiK5Lyg5a6M5oiQ5bm25YiH5o2i5qCH562+6aG1CiAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNoZWNrSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cmxGaWVsZCA9IGRpYWxvZy5nZXRDb250ZW50RWxlbWVudCgnaW5mbycsICd0eHRVcmwnKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodXJsRmllbGQgJiYgdXJsRmllbGQuZ2V0VmFsdWUoKSAmJiB1cmxGaWVsZC5nZXRWYWx1ZSgpLnN0YXJ0c1dpdGgoJy8nKSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCkKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5YiH5o2i5Yiw5Zu+5YOP5L+h5oGv5qCH562+6aG1CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlhbG9nLnNlbGVjdFBhZ2UoJ2luZm8nKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOW/veeVpemUmeivrwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgIH0sIDUwMCkKCiAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gMTDnp5LlkI7lgZzmraLmo4Dmn6UKICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCksIDEwMDAwKQogICAgICAgICAgICAgICAgICAgICAgICB9LCAxMDAwKQogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0pCgoKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pCgogICAgICAgICAgICB9IGNhdGNoIChmYWxsYmFja0Vycm9yKSB7CgogICAgICAgICAgICAgIHRoaXMuc2hvd0ZhbGxiYWNrRWRpdG9yID0gdHJ1ZQogICAgICAgICAgICAgIHJldHVybgogICAgICAgICAgICB9CiAgICAgICAgICB9CgogICAgICAgICAgLy8g55uR5ZCs5YaF5a655Y+Y5YyWCiAgICAgICAgICBpZiAodGhpcy5yaWNoRWRpdG9yICYmIHRoaXMucmljaEVkaXRvci5vbikgewogICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3Iub24oJ2NoYW5nZScsICgpID0+IHsKICAgICAgICAgICAgICBjb25zdCByYXdDb250ZW50ID0gdGhpcy5yaWNoRWRpdG9yLmdldERhdGEoKQogICAgICAgICAgICAgIGNvbnN0IGNvbnRlbnRXaXRoUmVsYXRpdmVVcmxzID0gdGhpcy5jb252ZXJ0VXJsc1RvUmVsYXRpdmUocmF3Q29udGVudCkKCiAgICAgICAgICAgICAgLy8g5L+d5a2YSFRNTOWGheWuueeUqOS6jumihOiniAogICAgICAgICAgICAgIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCA9IHRoaXMucHJlc2VydmVSaWNoVGV4dEZvcm1hdHRpbmcoY29udGVudFdpdGhSZWxhdGl2ZVVybHMpCiAgICAgICAgICAgICAgLy8g5L+d5a2Y57qv5paH5pys5YaF5a6555So5LqO6Kej5p6QCiAgICAgICAgICAgICAgdGhpcy5kb2N1bWVudENvbnRlbnQgPSB0aGlzLnN0cmlwSHRtbFRhZ3NLZWVwSW1hZ2VzKGNvbnRlbnRXaXRoUmVsYXRpdmVVcmxzKQoKICAgICAgICAgICAgICB0aGlzLmxhc3RTYXZlVGltZSA9IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKQoKICAgICAgICAgICAgICAvLyDmoIforrDmnInmnKrkv53lrZjnmoTmm7TmlLnlubbkv53lrZjliLDnvJPlrZgKICAgICAgICAgICAgICB0aGlzLmhhc1Vuc2F2ZWRDaGFuZ2VzID0gdHJ1ZQogICAgICAgICAgICAgIHRoaXMuc2F2ZVRvQ2FjaGUoKQogICAgICAgICAgICB9KQogICAgICAgICAgfQoKICAgICAgICAgIC8vIOebkeWQrOaMiemUruS6i+S7tgogICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLm9uKCdrZXknLCAoKSA9PiB7CiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgIGNvbnN0IHJhd0NvbnRlbnQgPSB0aGlzLnJpY2hFZGl0b3IuZ2V0RGF0YSgpCiAgICAgICAgICAgICAgY29uc3QgY29udGVudFdpdGhSZWxhdGl2ZVVybHMgPSB0aGlzLmNvbnZlcnRVcmxzVG9SZWxhdGl2ZShyYXdDb250ZW50KQoKICAgICAgICAgICAgICAvLyDkv53lrZhIVE1M5YaF5a6555So5LqO6aKE6KeICiAgICAgICAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gdGhpcy5wcmVzZXJ2ZVJpY2hUZXh0Rm9ybWF0dGluZyhjb250ZW50V2l0aFJlbGF0aXZlVXJscykKICAgICAgICAgICAgICAvLyDkv53lrZjnuq/mlofmnKzlhoXlrrnnlKjkuo7op6PmnpAKICAgICAgICAgICAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9IHRoaXMuc3RyaXBIdG1sVGFnc0tlZXBJbWFnZXMoY29udGVudFdpdGhSZWxhdGl2ZVVybHMpCgogICAgICAgICAgICAgIC8vIOagh+iusOacieacquS/neWtmOeahOabtOaUueW5tuS/neWtmOWIsOe8k+WtmAogICAgICAgICAgICAgIHRoaXMuaGFzVW5zYXZlZENoYW5nZXMgPSB0cnVlCiAgICAgICAgICAgICAgdGhpcy5zYXZlVG9DYWNoZSgpCiAgICAgICAgICAgIH0sIDEwMCkKICAgICAgICAgIH0pCgogICAgICAgICAgLy8g55uR5ZCs5a6e5L6L5YeG5aSH5bCx57uqCiAgICAgICAgICB0aGlzLnJpY2hFZGl0b3Iub24oJ2luc3RhbmNlUmVhZHknLCAoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQgPSB0cnVlCiAgICAgICAgICAgIC8vIOS8mOWFiOWKoOi9vee8k+WtmOeahEhUTUzlhoXlrrnvvIzlpoLmnpzmsqHmnInliJnkvb/nlKhkb2N1bWVudENvbnRlbnQKICAgICAgICAgICAgY29uc3QgY29udGVudFRvTG9hZCA9IHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCB8fCB0aGlzLmRvY3VtZW50Q29udGVudAogICAgICAgICAgICBpZiAoY29udGVudFRvTG9hZCkgewogICAgICAgICAgICAgIHRoaXMucmljaEVkaXRvci5zZXREYXRhKGNvbnRlbnRUb0xvYWQpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfSkKCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CgogICAgICAgIC8vIOWmguaenENLRWRpdG9y5Yid5aeL5YyW5aSx6LSl77yM5Zue6YCA5Yiw5pmu6YCa5paH5pys5qGGCiAgICAgICAgdGhpcy5mYWxsYmFja1RvVGV4dGFyZWEoKQogICAgICB9CiAgICB9LAoKICAgIC8vIOWbnumAgOWIsOaZrumAmuaWh+acrOahhgogICAgZmFsbGJhY2tUb1RleHRhcmVhKCkgewogICAgICBjb25zdCBlZGl0b3JDb250YWluZXIgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgncmljaC1lZGl0b3InKQogICAgICBpZiAoZWRpdG9yQ29udGFpbmVyKSB7CiAgICAgICAgY29uc3QgdGV4dGFyZWEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCd0ZXh0YXJlYScpCiAgICAgICAgdGV4dGFyZWEuY2xhc3NOYW1lID0gJ2ZhbGxiYWNrLXRleHRhcmVhJwogICAgICAgIHRleHRhcmVhLnBsYWNlaG9sZGVyID0gJ+ivt+WcqOatpOWkhOeymOi0tOaIlui+k+WFpemimOebruWGheWuuS4uLicKICAgICAgICB0ZXh0YXJlYS52YWx1ZSA9IHRoaXMuZG9jdW1lbnRDb250ZW50IHx8ICcnCiAgICAgICAgdGV4dGFyZWEuc3R5bGUuY3NzVGV4dCA9ICd3aWR0aDogMTAwJTsgaGVpZ2h0OiA0MDBweDsgYm9yZGVyOiAxcHggc29saWQgI2RkZDsgcGFkZGluZzogMTBweDsgZm9udC1mYW1pbHk6ICJDb3VyaWVyIE5ldyIsIG1vbm9zcGFjZTsgZm9udC1zaXplOiAxNHB4OyBsaW5lLWhlaWdodDogMS42OyByZXNpemU6IG5vbmU7JwoKICAgICAgICAvLyDnm5HlkKzlhoXlrrnlj5jljJYKICAgICAgICB0ZXh0YXJlYS5hZGRFdmVudExpc3RlbmVyKCdpbnB1dCcsIChlKSA9PiB7CiAgICAgICAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9IGUudGFyZ2V0LnZhbHVlCiAgICAgICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSBlLnRhcmdldC52YWx1ZSAvLyDnuq/mlofmnKzmqKHlvI/kuItIVE1M5YaF5a655LiO5paH5pys5YaF5a6555u45ZCMCiAgICAgICAgICB0aGlzLmxhc3RTYXZlVGltZSA9IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKQoKICAgICAgICAgIC8vIOagh+iusOacieacquS/neWtmOeahOabtOaUueW5tuS/neWtmOWIsOe8k+WtmAogICAgICAgICAgdGhpcy5oYXNVbnNhdmVkQ2hhbmdlcyA9IHRydWUKICAgICAgICAgIHRoaXMuc2F2ZVRvQ2FjaGUoKQogICAgICAgIH0pCgogICAgICAgIGVkaXRvckNvbnRhaW5lci5pbm5lckhUTUwgPSAnJwogICAgICAgIGVkaXRvckNvbnRhaW5lci5hcHBlbmRDaGlsZCh0ZXh0YXJlYSkKICAgICAgICB0aGlzLmVkaXRvckluaXRpYWxpemVkID0gdHJ1ZQogICAgICB9CiAgICB9LAoKICAgIC8vIOWOu+mZpEhUTUzmoIfnrb4KICAgIHN0cmlwSHRtbFRhZ3MoaHRtbCkgewogICAgICBjb25zdCBkaXYgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdkaXYnKQogICAgICBkaXYuaW5uZXJIVE1MID0gaHRtbAogICAgICByZXR1cm4gZGl2LnRleHRDb250ZW50IHx8IGRpdi5pbm5lclRleHQgfHwgJycKICAgIH0sCgogICAgLy8g6K6+572u57yW6L6R5Zmo5YaF5a65CiAgICBzZXRFZGl0b3JDb250ZW50KGNvbnRlbnQpIHsKCiAgICAgIGlmICh0aGlzLnJpY2hFZGl0b3IgJiYgdGhpcy5lZGl0b3JJbml0aWFsaXplZCkgewogICAgICAgIHRoaXMucmljaEVkaXRvci5zZXREYXRhKGNvbnRlbnQpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5aaC5p6c57yW6L6R5Zmo6L+Y5pyq5Yid5aeL5YyW77yM5L+d5a2Y5YaF5a65562J5b6F5Yid5aeL5YyW5a6M5oiQ5ZCO6K6+572uCiAgICAgICAgdGhpcy5kb2N1bWVudENvbnRlbnQgPSBjb250ZW50CiAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gY29udGVudCAvLyDlkIzml7borr7nva5IVE1M5YaF5a65CiAgICAgIH0KICAgIH0sCgoKCiAgICAvLyDpmLLmipblh73mlbAKICAgIGRlYm91bmNlKGZ1bmMsIHdhaXQpIHsKICAgICAgbGV0IHRpbWVvdXQKICAgICAgcmV0dXJuIGZ1bmN0aW9uIGV4ZWN1dGVkRnVuY3Rpb24oLi4uYXJncykgewogICAgICAgIGNvbnN0IGxhdGVyID0gKCkgPT4gewogICAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpCiAgICAgICAgICBmdW5jKC4uLmFyZ3MpCiAgICAgICAgfQogICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0KQogICAgICAgIHRpbWVvdXQgPSBzZXRUaW1lb3V0KGxhdGVyLCB3YWl0KQogICAgICB9CiAgICB9LAoKICAgIC8vIOWwhue8lui+keWZqOWGheWuueS4reeahOWujOaVtFVSTOi9rOaNouS4uuebuOWvuei3r+W+hAogICAgY29udmVydFVybHNUb1JlbGF0aXZlKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50KSByZXR1cm4gY29udGVudAoKICAgICAgLy8g5Yy56YWN5b2T5YmN5Z+f5ZCN55qE5a6M5pW0VVJM5bm26L2s5o2i5Li655u45a+56Lev5b6ECiAgICAgIGNvbnN0IGN1cnJlbnRPcmlnaW4gPSB3aW5kb3cubG9jYXRpb24ub3JpZ2luCiAgICAgIGNvbnN0IHVybFJlZ2V4ID0gbmV3IFJlZ0V4cChjdXJyZW50T3JpZ2luLnJlcGxhY2UoL1suKis/XiR7fSgpfFtcXVxcXS9nLCAnXFwkJicpICsgJygvW14iXCdcXHM+XSopJywgJ2cnKQoKICAgICAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSh1cmxSZWdleCwgJyQxJykKICAgIH0sCgogICAgLy8g6Kej5p6Q5paH5qGjCiAgICBwYXJzZURvY3VtZW50KCkgewogICAgICBpZiAoIXRoaXMuZG9jdW1lbnRDb250ZW50LnRyaW0oKSkgewogICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBwYXJzZVJlc3VsdCA9IHRoaXMucGFyc2VRdWVzdGlvbkNvbnRlbnQodGhpcy5kb2N1bWVudENvbnRlbnQpCiAgICAgICAgLy8g5Li65q+P5Liq6aKY55uu5re75YqgY29sbGFwc2Vk5bGe5oCnCiAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBwYXJzZVJlc3VsdC5xdWVzdGlvbnMubWFwKHF1ZXN0aW9uID0+ICh7CiAgICAgICAgICAuLi5xdWVzdGlvbiwKICAgICAgICAgIGNvbGxhcHNlZDogZmFsc2UKICAgICAgICB9KSkKICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gcGFyc2VSZXN1bHQuZXJyb3JzCgogICAgICAgIC8vIOabtOaWsOS/neWtmOaXtumXtAogICAgICAgIHRoaXMubGFzdFNhdmVUaW1lID0gbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygpCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CgogICAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSBbJ+ino+aekOWksei0pe+8micgKyBlcnJvci5tZXNzYWdlXQogICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgfQogICAgfSwKCiAgICAvLyDop6PmnpDpopjnm67lhoXlrrkgLSDkvJjljJbniYjmnKzvvIzmm7TliqDlgaXlo64KICAgIHBhcnNlUXVlc3Rpb25Db250ZW50KGNvbnRlbnQpIHsKICAgICAgY29uc3QgcXVlc3Rpb25zID0gW10KICAgICAgY29uc3QgZXJyb3JzID0gW10KCiAgICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKCiAgICAgICAgcmV0dXJuIHsgcXVlc3Rpb25zLCBlcnJvcnM6IFsn6Kej5p6Q5YaF5a655Li656m65oiW5qC85byP5LiN5q2j56GuJ10gfQogICAgICB9CgogICAgICB0cnkgewoKCiAgICAgICAgLy8g5L+d55WZ5Zu+54mH5qCH562+77yM5Y+q56e76Zmk5YW25LuWSFRNTOagh+etvgogICAgICAgIGNvbnN0IHRleHRDb250ZW50ID0gdGhpcy5zdHJpcEh0bWxUYWdzS2VlcEltYWdlcyhjb250ZW50KQoKICAgICAgICBpZiAoIXRleHRDb250ZW50IHx8IHRleHRDb250ZW50LnRyaW0oKS5sZW5ndGggPT09IDApIHsKCiAgICAgICAgICByZXR1cm4geyBxdWVzdGlvbnMsIGVycm9yczogWyflpITnkIblkI7nmoTlhoXlrrnkuLrnqbonXSB9CiAgICAgICAgfQoKICAgICAgICAvLyDmjInooYzliIblibLlhoXlrrkKICAgICAgICBjb25zdCBsaW5lcyA9IHRleHRDb250ZW50LnNwbGl0KCdcbicpLm1hcChsaW5lID0+IGxpbmUudHJpbSgpKS5maWx0ZXIobGluZSA9PiBsaW5lLmxlbmd0aCA+IDApCgoKICAgICAgICBpZiAobGluZXMubGVuZ3RoID09PSAwKSB7CgogICAgICAgICAgcmV0dXJuIHsgcXVlc3Rpb25zLCBlcnJvcnM6IFsn5rKh5pyJ5pyJ5pWI55qE5YaF5a656KGMJ10gfQogICAgICAgIH0KCgoKICAgICAgICBsZXQgY3VycmVudFF1ZXN0aW9uTGluZXMgPSBbXQogICAgICAgIGxldCBxdWVzdGlvbk51bWJlciA9IDAKCiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2ldCgogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5piv6aKY55uu5byA5aeL6KGM77ya5pWw5a2X44CBW+mimOebruexu+Wei10g5oiWIFvpopjnm67nsbvlnotdCiAgICAgICAgICBjb25zdCBpc1F1ZXN0aW9uU3RhcnQgPSB0aGlzLmlzUXVlc3Rpb25TdGFydExpbmUobGluZSkgfHwgdGhpcy5pc1F1ZXN0aW9uVHlwZVN0YXJ0KGxpbmUpCgogICAgICAgICAgaWYgKGlzUXVlc3Rpb25TdGFydCkgewogICAgICAgICAgICAvLyDlpoLmnpzkuYvliY3mnInpopjnm67lhoXlrrnvvIzlhYjlpITnkIbkuYvliY3nmoTpopjnm64KICAgICAgICAgICAgaWYgKGN1cnJlbnRRdWVzdGlvbkxpbmVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgY29uc3QgcXVlc3Rpb25UZXh0ID0gY3VycmVudFF1ZXN0aW9uTGluZXMuam9pbignXG4nKQogICAgICAgICAgICAgICAgY29uc3QgcGFyc2VkUXVlc3Rpb24gPSB0aGlzLnBhcnNlUXVlc3Rpb25Gcm9tTGluZXMocXVlc3Rpb25UZXh0LCBxdWVzdGlvbk51bWJlcikKICAgICAgICAgICAgICAgIGlmIChwYXJzZWRRdWVzdGlvbikgewogICAgICAgICAgICAgICAgICBxdWVzdGlvbnMucHVzaChwYXJzZWRRdWVzdGlvbikKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICAgICAgZXJyb3JzLnB1c2goYOesrCAke3F1ZXN0aW9uTnVtYmVyfSDpopjop6PmnpDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihg4p2MIOmimOebriAke3F1ZXN0aW9uTnVtYmVyfSDop6PmnpDlpLHotKU6YCwgZXJyb3IpCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CgogICAgICAgICAgICAvLyDlvIDlp4vmlrDpopjnm64KICAgICAgICAgICAgY3VycmVudFF1ZXN0aW9uTGluZXMgPSBbbGluZV0KICAgICAgICAgICAgcXVlc3Rpb25OdW1iZXIrKwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgLy8g5aaC5p6c5b2T5YmN5Zyo5aSE55CG6aKY55uu5Lit77yM5re75Yqg5Yiw5b2T5YmN6aKY55uuCiAgICAgICAgICAgIGlmIChjdXJyZW50UXVlc3Rpb25MaW5lcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgY3VycmVudFF1ZXN0aW9uTGluZXMucHVzaChsaW5lKQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAvLyDlpITnkIbmnIDlkI7kuIDkuKrpopjnm64KICAgICAgICBpZiAoY3VycmVudFF1ZXN0aW9uTGluZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgY29uc3QgcXVlc3Rpb25UZXh0ID0gY3VycmVudFF1ZXN0aW9uTGluZXMuam9pbignXG4nKQogICAgICAgICAgICBjb25zdCBwYXJzZWRRdWVzdGlvbiA9IHRoaXMucGFyc2VRdWVzdGlvbkZyb21MaW5lcyhxdWVzdGlvblRleHQsIHF1ZXN0aW9uTnVtYmVyKQogICAgICAgICAgICBpZiAocGFyc2VkUXVlc3Rpb24pIHsKICAgICAgICAgICAgICBxdWVzdGlvbnMucHVzaChwYXJzZWRRdWVzdGlvbikKICAgICAgICAgICAgfQogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgZXJyb3JzLnB1c2goYOesrCAke3F1ZXN0aW9uTnVtYmVyfSDpopjop6PmnpDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQogICAgICAgICAgICBjb25zb2xlLmVycm9yKGDinYwg5pyA5ZCO6aKY55uuICR7cXVlc3Rpb25OdW1iZXJ9IOino+aekOWksei0pTpgLCBlcnJvcikKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGVycm9ycy5wdXNoKGDmlofmoaPop6PmnpDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDmlofmoaPop6PmnpDlpLHotKU6JywgZXJyb3IpCiAgICAgIH0KCiAgICAgIGNvbnNvbGUubG9nKCfop6PmnpDlrozmiJDvvIzlhbEnLCBxdWVzdGlvbnMubGVuZ3RoLCAn6YGT6aKY55uu77yMJywgZXJyb3JzLmxlbmd0aCwgJ+S4qumUmeivrycpCiAgICAgIHJldHVybiB7IHF1ZXN0aW9ucywgZXJyb3JzIH0KICAgIH0sCgogICAgLy8g5Yik5pat5piv5ZCm5Li66aKY55uu5byA5aeL6KGMIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBpc1F1ZXN0aW9uU3RhcnRMaW5lKGxpbmUpIHsKICAgICAgLy8g6KeE6IyD77ya5q+P6aKY5YmN6Z2i6ZyA6KaB5Yqg5LiK6aKY5Y+35qCH6K+G77yM6aKY5Y+35ZCO6Z2i6ZyA6KaB5Yqg5LiK56ym5Y+377yIOu+8muOAgS7vvI7vvIkKICAgICAgLy8g5Yy56YWN5qC85byP77ya5pWw5a2XICsg56ym5Y+3KDrvvJrjgIEu77yOKSArIOWPr+mAieepuuagvAogICAgICAvLyDkvovlpoLvvJoxLiAx44CBIDHvvJogMe+8jiDnrYkKICAgICAgcmV0dXJuIC9eXGQrWy4677ya77yO44CBXVxzKi8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrpopjlnovmoIfms6jlvIDlp4vooYwKICAgIGlzUXVlc3Rpb25UeXBlU3RhcnQobGluZSkgewogICAgICAvLyDljLnphY3moLzlvI/vvJpb6aKY55uu57G75Z6LXQogICAgICAvLyDkvovlpoLvvJpb5Y2V6YCJ6aKYXSBb5aSa6YCJ6aKYXSBb5Yik5pat6aKYXSDnrYkKICAgICAgcmV0dXJuIC9eXFsuKj/pophcXS8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDku47ooYzmlbDnu4Top6PmnpDljZXkuKrpopjnm64gLSDmjInnhafovpPlhaXop4TojIMKICAgIHBhcnNlUXVlc3Rpb25Gcm9tTGluZXMocXVlc3Rpb25UZXh0KSB7CiAgICAgIGNvbnN0IGxpbmVzID0gcXVlc3Rpb25UZXh0LnNwbGl0KCdcbicpLm1hcChsaW5lID0+IGxpbmUudHJpbSgpKS5maWx0ZXIobGluZSA9PiBsaW5lLmxlbmd0aCA+IDApCgogICAgICBpZiAobGluZXMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfpopjnm67lhoXlrrnkuLrnqbonKQogICAgICB9CgogICAgICBsZXQgcXVlc3Rpb25UeXBlID0gJ2p1ZGdtZW50JyAvLyDpu5jorqTliKTmlq3popgKICAgICAgbGV0IHF1ZXN0aW9uQ29udGVudCA9ICcnCiAgICAgIGxldCBjb250ZW50U3RhcnRJbmRleCA9IDAKCiAgICAgIC8vIOajgOafpeaYr+WQpuaciemimOWei+agh+azqO+8iOWmgiBb5Y2V6YCJ6aKYXeOAgVvlpJrpgInpophd44CBW+WIpOaWremimF3vvIkKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXQogICAgICAgIGNvbnN0IHR5cGVNYXRjaCA9IGxpbmUubWF0Y2goL1xbKC4qP+mimClcXS8pCiAgICAgICAgaWYgKHR5cGVNYXRjaCkgewogICAgICAgICAgY29uc3QgdHlwZVRleHQgPSB0eXBlTWF0Y2hbMV0KCiAgICAgICAgICAvLyDovazmjaLpopjnm67nsbvlnosKICAgICAgICAgIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn5Yik5patJykpIHsKICAgICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ2p1ZGdtZW50JwogICAgICAgICAgfSBlbHNlIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn5Y2V6YCJJykpIHsKICAgICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ3NpbmdsZScKICAgICAgICAgIH0gZWxzZSBpZiAodHlwZVRleHQuaW5jbHVkZXMoJ+WkmumAiScpKSB7CiAgICAgICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdtdWx0aXBsZScKICAgICAgICAgIH0gZWxzZSBpZiAodHlwZVRleHQuaW5jbHVkZXMoJ+Whq+epuicpKSB7CiAgICAgICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdmaWxsJwogICAgICAgICAgfSBlbHNlIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn566A562UJykpIHsKICAgICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ2Vzc2F5JwogICAgICAgICAgfQoKICAgICAgICAgIC8vIOWmguaenOmimOWei+agh+azqOWSjOmimOebruWGheWuueWcqOWQjOS4gOihjAogICAgICAgICAgY29uc3QgcmVtYWluaW5nQ29udGVudCA9IGxpbmUucmVwbGFjZSgvXFsuKj/pophcXS8sICcnKS50cmltKCkKICAgICAgICAgIGlmIChyZW1haW5pbmdDb250ZW50KSB7CiAgICAgICAgICAgIHF1ZXN0aW9uQ29udGVudCA9IHJlbWFpbmluZ0NvbnRlbnQKICAgICAgICAgICAgY29udGVudFN0YXJ0SW5kZXggPSBpICsgMQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgY29udGVudFN0YXJ0SW5kZXggPSBpICsgMQogICAgICAgICAgfQogICAgICAgICAgYnJlYWsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOWmguaenOayoeacieaJvuWIsOmimOWei+agh+azqO+8jOS7juesrOS4gOihjOW8gOWni+ino+aekAogICAgICBpZiAoY29udGVudFN0YXJ0SW5kZXggPT09IDApIHsKICAgICAgICBjb250ZW50U3RhcnRJbmRleCA9IDAKICAgICAgfQoKICAgICAgLy8g5o+Q5Y+W6aKY55uu5YaF5a6577yI5LuO6aKY5Y+36KGM5byA5aeL77yJCiAgICAgIGZvciAobGV0IGkgPSBjb250ZW50U3RhcnRJbmRleDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2ldCgogICAgICAgIC8vIOWmguaenOaYr+mimOWPt+ihjO+8jOaPkOWPlumimOebruWGheWuue+8iOenu+mZpOmimOWPt++8iQogICAgICAgIGlmICh0aGlzLmlzUXVlc3Rpb25TdGFydExpbmUobGluZSkpIHsKICAgICAgICAgIC8vIOenu+mZpOmimOWPt++8jOaPkOWPlumimOebruWGheWuuQogICAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gbGluZS5yZXBsYWNlKC9eXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKICAgICAgICAgIGNvbnRlbnRTdGFydEluZGV4ID0gaSArIDEKICAgICAgICAgIGJyZWFrCiAgICAgICAgfSBlbHNlIGlmICghcXVlc3Rpb25Db250ZW50KSB7CiAgICAgICAgICAvLyDlpoLmnpzov5jmsqHmnInpopjnm67lhoXlrrnvvIzlvZPliY3ooYzlsLHmmK/popjnm67lhoXlrrkKICAgICAgICAgIHF1ZXN0aW9uQ29udGVudCA9IGxpbmUKICAgICAgICAgIGNvbnRlbnRTdGFydEluZGV4ID0gaSArIDEKICAgICAgICAgIGJyZWFrCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDnu6fnu63mlLbpm4bpopjnm67lhoXlrrnvvIjnm7TliLDpgYfliLDpgInpobnmiJbnrZTmoYjvvIkKICAgICAgZm9yIChsZXQgaSA9IGNvbnRlbnRTdGFydEluZGV4OyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgLy8g5aaC5p6c6YGH5Yiw6YCJ6aG56KGM44CB562U5qGI6KGM44CB6Kej5p6Q6KGM5oiW6Zq+5bqm6KGM77yM5YGc5q2i5pS26ZuG6aKY55uu5YaF5a65CiAgICAgICAgaWYgKHRoaXMuaXNPcHRpb25MaW5lKGxpbmUpIHx8IHRoaXMuaXNBbnN3ZXJMaW5lKGxpbmUpIHx8CiAgICAgICAgICAgIHRoaXMuaXNFeHBsYW5hdGlvbkxpbmUobGluZSkgfHwgdGhpcy5pc0RpZmZpY3VsdHlMaW5lKGxpbmUpKSB7CiAgICAgICAgICBicmVhawogICAgICAgIH0KCiAgICAgICAgLy8g57un57ut5re75Yqg5Yiw6aKY55uu5YaF5a6577yM5L2G6KaB56Gu5L+d5LiN5YyF5ZCr6aKY5Y+3CiAgICAgICAgbGV0IGNsZWFuTGluZSA9IGxpbmUKICAgICAgICAvLyDlpoLmnpzov5nooYzov5jljIXlkKvpopjlj7fvvIznp7vpmaTlroMKICAgICAgICBpZiAodGhpcy5pc1F1ZXN0aW9uU3RhcnRMaW5lKGxpbmUpKSB7CiAgICAgICAgICBjbGVhbkxpbmUgPSBsaW5lLnJlcGxhY2UoL15cZCtbLjrvvJrvvI7jgIFdXHMqLywgJycpLnRyaW0oKQogICAgICAgIH0KCiAgICAgICAgaWYgKGNsZWFuTGluZSkgewogICAgICAgICAgaWYgKHF1ZXN0aW9uQ29udGVudCkgewogICAgICAgICAgICBxdWVzdGlvbkNvbnRlbnQgKz0gJ1xuJyArIGNsZWFuTGluZQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gY2xlYW5MaW5lCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICBpZiAoIXF1ZXN0aW9uQ29udGVudCkgewogICAgICAgIHRocm93IG5ldyBFcnJvcign5peg5rOV5o+Q5Y+W6aKY55uu5YaF5a65JykKICAgICAgfQoKICAgICAgLy8g5pyA57uI5riF55CG77ya56Gu5L+d6aKY55uu5YaF5a655LiN5YyF5ZCr6aKY5Y+3CiAgICAgIGxldCBmaW5hbFF1ZXN0aW9uQ29udGVudCA9IHF1ZXN0aW9uQ29udGVudC50cmltKCkKICAgICAgLy8g5L2/55So5pu05by655qE5riF55CG6YC76L6R77yM5aSa5qyh5riF55CG56Gu5L+d5b275bqV56e76Zmk6aKY5Y+3CiAgICAgIHdoaWxlICgvXlxzKlxkK1suOu+8mu+8juOAgV0vLnRlc3QoZmluYWxRdWVzdGlvbkNvbnRlbnQpKSB7CiAgICAgICAgZmluYWxRdWVzdGlvbkNvbnRlbnQgPSBmaW5hbFF1ZXN0aW9uQ29udGVudC5yZXBsYWNlKC9eXHMqXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKICAgICAgfQoKICAgICAgLy8g6aKd5aSW5riF55CG77ya56e76Zmk5Y+v6IO955qESFRNTOagh+etvuWGheeahOmimOWPtwogICAgICBpZiAoZmluYWxRdWVzdGlvbkNvbnRlbnQuaW5jbHVkZXMoJzwnKSkgewogICAgICAgIGZpbmFsUXVlc3Rpb25Db250ZW50ID0gdGhpcy5yZW1vdmVRdWVzdGlvbk51bWJlcihmaW5hbFF1ZXN0aW9uQ29udGVudCkKICAgICAgfQoKICAgICAgY29uc3QgcXVlc3Rpb24gPSB7CiAgICAgICAgcXVlc3Rpb25UeXBlOiBxdWVzdGlvblR5cGUsCiAgICAgICAgdHlwZTogcXVlc3Rpb25UeXBlLAogICAgICAgIHR5cGVOYW1lOiB0aGlzLmdldFR5cGVEaXNwbGF5TmFtZShxdWVzdGlvblR5cGUpLAogICAgICAgIHF1ZXN0aW9uQ29udGVudDogZmluYWxRdWVzdGlvbkNvbnRlbnQsCiAgICAgICAgY29udGVudDogZmluYWxRdWVzdGlvbkNvbnRlbnQsCiAgICAgICAgZGlmZmljdWx0eTogJycsIC8vIOS4jeiuvue9rum7mOiupOWAvAogICAgICAgIGV4cGxhbmF0aW9uOiAnJywKICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICBjb3JyZWN0QW5zd2VyOiAnJywKICAgICAgICBjb2xsYXBzZWQ6IGZhbHNlICAvLyDpu5jorqTlsZXlvIAKICAgICAgfQoKICAgICAgLy8g6Kej5p6Q6YCJ6aG577yI5a+55LqO6YCJ5oup6aKY77yJCiAgICAgIGNvbnN0IG9wdGlvblJlc3VsdCA9IHRoaXMucGFyc2VPcHRpb25zRnJvbUxpbmVzKGxpbmVzLCAwKQogICAgICBxdWVzdGlvbi5vcHRpb25zID0gb3B0aW9uUmVzdWx0Lm9wdGlvbnMKCiAgICAgIC8vIOagueaNrumAiemhueaVsOmHj+aOqOaWremimOebruexu+Wei++8iOWmguaenOS5i+WJjeayoeacieaYjuehruagh+azqO+8iQogICAgICBpZiAocXVlc3Rpb25UeXBlID09PSAnanVkZ21lbnQnICYmIHF1ZXN0aW9uLm9wdGlvbnMubGVuZ3RoID4gMCkgewogICAgICAgIC8vIOWmguaenOaciemAiemhue+8jOaOqOaWreS4uumAieaLqemimAogICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdzaW5nbGUnICAvLyDpu5jorqTkuLrljZXpgInpopgKICAgICAgICBxdWVzdGlvbi5xdWVzdGlvblR5cGUgPSBxdWVzdGlvblR5cGUKICAgICAgICBxdWVzdGlvbi50eXBlID0gcXVlc3Rpb25UeXBlCiAgICAgICAgcXVlc3Rpb24udHlwZU5hbWUgPSB0aGlzLmdldFR5cGVEaXNwbGF5TmFtZShxdWVzdGlvblR5cGUpCiAgICAgIH0KCiAgICAgIC8vIOino+aekOetlOahiOOAgeino+aekOOAgemavuW6pgogICAgICB0aGlzLnBhcnNlUXVlc3Rpb25NZXRhRnJvbUxpbmVzKGxpbmVzLCBxdWVzdGlvbikKCiAgICAgIC8vIOagueaNruetlOahiOmVv+W6pui/m+S4gOatpeaOqOaWremAieaLqemimOexu+WeiwogICAgICBpZiAocXVlc3Rpb25UeXBlID09PSAnc2luZ2xlJyAmJiBxdWVzdGlvbi5jb3JyZWN0QW5zd2VyICYmIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIubGVuZ3RoID4gMSkgewogICAgICAgIC8vIOWmguaenOetlOahiOWMheWQq+WkmuS4quWtl+avje+8jOaOqOaWreS4uuWkmumAiemimAogICAgICAgIGlmICgvXltBLVpdezIsfSQvLnRlc3QocXVlc3Rpb24uY29ycmVjdEFuc3dlcikpIHsKICAgICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdtdWx0aXBsZScKICAgICAgICAgIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSA9IHF1ZXN0aW9uVHlwZQogICAgICAgICAgcXVlc3Rpb24udHlwZSA9IHF1ZXN0aW9uVHlwZQogICAgICAgICAgcXVlc3Rpb24udHlwZU5hbWUgPSB0aGlzLmdldFR5cGVEaXNwbGF5TmFtZShxdWVzdGlvblR5cGUpCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDmnIDnu4jmuIXnkIbvvJrnoa7kv53popjnm67lhoXlrrnlrozlhajmsqHmnInpopjlj7cKICAgICAgcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50ID0gdGhpcy5yZW1vdmVRdWVzdGlvbk51bWJlcihxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQpCiAgICAgIHF1ZXN0aW9uLmNvbnRlbnQgPSBxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQKCiAgICAgIHJldHVybiBxdWVzdGlvbgogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrpgInpobnooYwgLSDmjInnhafovpPlhaXop4TojIMKICAgIGlzT3B0aW9uTGluZShsaW5lKSB7CiAgICAgIC8vIOinhOiMg++8mumAiemhueagvOW8j++8iEE677yJ77yM5a2X5q+N5Y+v5Lul5Li6QeWIsFrnmoTku7vmhI/lpKflsI/lhpnlrZfmr43vvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLoiOu+8muOAgS7vvI4i5YW25Lit5LmL5LiACiAgICAgIHJldHVybiAvXltBLVphLXpdWy4677ya77yO44CBXVxzKi8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrnrZTmoYjooYwgLSDmjInnhafovpPlhaXop4TojIMKICAgIGlzQW5zd2VyTGluZShsaW5lKSB7CiAgICAgIC8vIOinhOiMg++8muaYvuW8j+agh+azqOagvOW8j++8iOetlOahiO+8mu+8ie+8jOWGkuWPt+WPr+S7peabv+aNouS4uiAiOu+8muOAgSLlhbbkuK3kuYvkuIAKICAgICAgcmV0dXJuIC9e562U5qGIWy4677ya44CBXVxzKi8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrop6PmnpDooYwgLSDmjInnhafovpPlhaXop4TojIMKICAgIGlzRXhwbGFuYXRpb25MaW5lKGxpbmUpIHsKICAgICAgLy8g6KeE6IyD77ya6Kej5p6Q5qC85byP77yI6Kej5p6Q77ya77yJ77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6ICI677ya44CBIuWFtuS4reS5i+S4gAogICAgICByZXR1cm4gL17op6PmnpBbLjrvvJrjgIFdXHMqLy50ZXN0KGxpbmUpCiAgICB9LAoKICAgIC8vIOWIpOaWreaYr+WQpuS4uumavuW6puihjCAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgaXNEaWZmaWN1bHR5TGluZShsaW5lKSB7CiAgICAgIC8vIOinhOiMg++8mumavuW6puagvOW8j++8iOmavuW6pu+8mu+8ie+8jOWGkuWPt+WPr+S7peabv+aNouS4uiAiOu+8muOAgSLlhbbkuK3kuYvkuIAKICAgICAgcmV0dXJuIC9e6Zq+5bqmWy4677ya44CBXVxzKi8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDojrflj5bpopjnm67nsbvlnovmmL7npLrlkI3np7AKICAgIGdldFR5cGVEaXNwbGF5TmFtZSh0eXBlKSB7CiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7CiAgICAgICAgJ2p1ZGdtZW50JzogJ+WIpOaWremimCcsCiAgICAgICAgJ3NpbmdsZSc6ICfljZXpgInpopgnLAogICAgICAgICdtdWx0aXBsZSc6ICflpJrpgInpopgnLAogICAgICAgICdmaWxsJzogJ+Whq+epuumimCcsCiAgICAgICAgJ2Vzc2F5JzogJ+eugOetlOmimCcKICAgICAgfQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCAn5Yik5pat6aKYJwogICAgfSwKCiAgICAvLyDlpITnkIblm77niYfot6/lvoTvvIzlsIbnm7jlr7not6/lvoTovazmjaLkuLrlrozmlbTot6/lvoQKICAgIHByb2Nlc3NJbWFnZVBhdGhzKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewogICAgICAgIHJldHVybiAnJwogICAgICB9CgogICAgICB0cnkgewoKICAgICAgICAvLyDlpITnkIZpbWfmoIfnrb7kuK3nmoTnm7jlr7not6/lvoQKICAgICAgICBjb25zdCBwcm9jZXNzZWRDb250ZW50ID0gY29udGVudC5yZXBsYWNlKC88aW1nKFtePl0qPylzcmM9IihbXiJdKj8pIihbXj5dKj8pPi9nLCAobWF0Y2gsIGJlZm9yZSwgc3JjLCBhZnRlcikgPT4gewogICAgICAgICAgaWYgKCFzcmMpIHJldHVybiBtYXRjaAoKCiAgICAgICAgICAvLyDlpoLmnpzlt7Lnu4/mmK/lrozmlbTot6/lvoTvvIzkuI3lpITnkIYKICAgICAgICAgIGlmIChzcmMuc3RhcnRzV2l0aCgnaHR0cDovLycpIHx8IHNyYy5zdGFydHNXaXRoKCdodHRwczovLycpIHx8IHNyYy5zdGFydHNXaXRoKCdkYXRhOicpKSB7CiAgICAgICAgICAgIHJldHVybiBtYXRjaAogICAgICAgICAgfQoKICAgICAgICAgIC8vIOWmguaenOaYr+ebuOWvuei3r+W+hO+8jOa3u+WKoOWQjuerr+acjeWKoeWZqOWcsOWdgAogICAgICAgICAgY29uc3QgZnVsbFNyYyA9ICdodHRwOi8vbG9jYWxob3N0Ojg4MDInICsgKHNyYy5zdGFydHNXaXRoKCcvJykgPyBzcmMgOiAnLycgKyBzcmMpCiAgICAgICAgICBjb25zdCByZXN1bHQgPSBgPGltZyR7YmVmb3JlfXNyYz0iJHtmdWxsU3JjfSIke2FmdGVyfT5gCiAgICAgICAgICByZXR1cm4gcmVzdWx0CiAgICAgICAgfSkKCiAgICAgICAgcmV0dXJuIHByb2Nlc3NlZENvbnRlbnQKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg5aSE55CG5Zu+54mH6Lev5b6E5pe25Ye66ZSZOicsIGVycm9yKQogICAgICAgIHJldHVybiBjb250ZW50CiAgICAgIH0KICAgIH0sCgogICAgLy8g5L+d55WZ5a+M5paH5pys5qC85byP55So5LqO6aKE6KeI5pi+56S6CiAgICBwcmVzZXJ2ZVJpY2hUZXh0Rm9ybWF0dGluZyhjb250ZW50KSB7CiAgICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICAvLyDkv53nlZnluLjnlKjnmoTlr4zmlofmnKzmoLzlvI/moIfnrb4KICAgICAgICBsZXQgcHJvY2Vzc2VkQ29udGVudCA9IGNvbnRlbnQKICAgICAgICAgIC8vIOi9rOaNouebuOWvuei3r+W+hOeahOWbvueJhwogICAgICAgICAgLnJlcGxhY2UoLzxpbWcoW14+XSo/KXNyYz0iKFteIl0qPykiKFtePl0qPyk+L2dpLCAobWF0Y2gsIGJlZm9yZSwgc3JjLCBhZnRlcikgPT4gewogICAgICAgICAgICBpZiAoIXNyYy5zdGFydHNXaXRoKCdodHRwJykgJiYgIXNyYy5zdGFydHNXaXRoKCdkYXRhOicpKSB7CiAgICAgICAgICAgICAgY29uc3QgZnVsbFNyYyA9IHRoaXMucHJvY2Vzc0ltYWdlUGF0aHMoc3JjKQogICAgICAgICAgICAgIHJldHVybiBgPGltZyR7YmVmb3JlfXNyYz0iJHtmdWxsU3JjfSIke2FmdGVyfT5gCiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIG1hdGNoCiAgICAgICAgICB9KQogICAgICAgICAgLy8g5L+d55WZ5q616JC957uT5p6ECiAgICAgICAgICAucmVwbGFjZSgvPHBbXj5dKj4vZ2ksICc8cD4nKQogICAgICAgICAgLnJlcGxhY2UoLzxcL3A+L2dpLCAnPC9wPicpCiAgICAgICAgICAvLyDkv53nlZnmjaLooYwKICAgICAgICAgIC5yZXBsYWNlKC88YnJccypcLz8+L2dpLCAnPGJyPicpCiAgICAgICAgICAvLyDmuIXnkIblpJrkvZnnmoTnqbrnmb3mrrXokL0KICAgICAgICAgIC5yZXBsYWNlKC88cD5ccyo8XC9wPi9naSwgJycpCiAgICAgICAgICAucmVwbGFjZSgvKDxwPltcc1xuXSo8XC9wPikvZ2ksICcnKQoKICAgICAgICByZXR1cm4gcHJvY2Vzc2VkQ29udGVudC50cmltKCkKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgcHJlc2VydmVSaWNoVGV4dEZvcm1hdHRpbmcg5Ye66ZSZOicsIGVycm9yKQogICAgICAgIHJldHVybiBjb250ZW50CiAgICAgIH0KICAgIH0sCgogICAgLy8g56e76ZmkSFRNTOagh+etvuS9huS/neeVmeWbvueJh+agh+etvgogICAgc3RyaXBIdG1sVGFnc0tlZXBJbWFnZXMoY29udGVudCkgewogICAgICBpZiAoIWNvbnRlbnQgfHwgdHlwZW9mIGNvbnRlbnQgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIHRyeSB7CgogICAgICAgIC8vIOWFiOS/neWtmOaJgOacieWbvueJh+agh+etvgogICAgICAgIGNvbnN0IGltYWdlcyA9IFtdCiAgICAgICAgbGV0IGltYWdlSW5kZXggPSAwCiAgICAgICAgY29uc3QgY29udGVudFdpdGhQbGFjZWhvbGRlcnMgPSBjb250ZW50LnJlcGxhY2UoLzxpbWdbXj5dKj4vZ2ksIChtYXRjaCkgPT4gewogICAgICAgICAgaW1hZ2VzLnB1c2gobWF0Y2gpCiAgICAgICAgICByZXR1cm4gYFxuX19JTUFHRV9QTEFDRUhPTERFUl8ke2ltYWdlSW5kZXgrK31fX1xuYAogICAgICAgIH0pCgogICAgICAgIC8vIOenu+mZpOWFtuS7lkhUTUzmoIfnrb7vvIzkvYbkv53nlZnmjaLooYwKICAgICAgICBsZXQgdGV4dENvbnRlbnQgPSBjb250ZW50V2l0aFBsYWNlaG9sZGVycwogICAgICAgICAgLnJlcGxhY2UoLzxiclxzKlwvPz4vZ2ksICdcbicpICAvLyBicuagh+etvui9rOaNouS4uuaNouihjAogICAgICAgICAgLnJlcGxhY2UoLzxcL3A+L2dpLCAnXG4nKSAgICAgICAvLyBw57uT5p2f5qCH562+6L2s5o2i5Li65o2i6KGMCiAgICAgICAgICAucmVwbGFjZSgvPHBbXj5dKj4vZ2ksICdcbicpICAgIC8vIHDlvIDlp4vmoIfnrb7ovazmjaLkuLrmjaLooYwKICAgICAgICAgIC5yZXBsYWNlKC88W14+XSo+L2csICcnKSAgICAgICAgLy8g56e76Zmk5YW25LuWSFRNTOagh+etvgogICAgICAgICAgLnJlcGxhY2UoL1xuXHMqXG4vZywgJ1xuJykgICAgICAvLyDlkIjlubblpJrkuKrmjaLooYwKCiAgICAgICAgLy8g5oGi5aSN5Zu+54mH5qCH562+CiAgICAgICAgbGV0IGZpbmFsQ29udGVudCA9IHRleHRDb250ZW50CiAgICAgICAgaW1hZ2VzLmZvckVhY2goKGltZywgaW5kZXgpID0+IHsKICAgICAgICAgIGNvbnN0IHBsYWNlaG9sZGVyID0gYF9fSU1BR0VfUExBQ0VIT0xERVJfJHtpbmRleH1fX2AKICAgICAgICAgIGlmIChmaW5hbENvbnRlbnQuaW5jbHVkZXMocGxhY2Vob2xkZXIpKSB7CiAgICAgICAgICAgIGZpbmFsQ29udGVudCA9IGZpbmFsQ29udGVudC5yZXBsYWNlKHBsYWNlaG9sZGVyLCBpbWcpCiAgICAgICAgICB9CiAgICAgICAgfSkKCiAgICAgICAgcmV0dXJuIGZpbmFsQ29udGVudC50cmltKCkKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgc3RyaXBIdG1sVGFnc0tlZXBJbWFnZXMg5Ye66ZSZOicsIGVycm9yKQogICAgICAgIHJldHVybiBjb250ZW50CiAgICAgIH0KICAgIH0sCgogICAgLy8g5LuO6KGM5pWw57uE6Kej5p6Q6YCJ6aG5IC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBwYXJzZU9wdGlvbnNGcm9tTGluZXMobGluZXMsIHN0YXJ0SW5kZXgpIHsKICAgICAgY29uc3Qgb3B0aW9ucyA9IFtdCgogICAgICBpZiAoIUFycmF5LmlzQXJyYXkobGluZXMpIHx8IHN0YXJ0SW5kZXggPCAwIHx8IHN0YXJ0SW5kZXggPj0gbGluZXMubGVuZ3RoKSB7CiAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8g6Kej5p6Q6YCJ6aG55Y+C5pWw5peg5pWIJykKICAgICAgICByZXR1cm4geyBvcHRpb25zIH0KICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKGxldCBpID0gc3RhcnRJbmRleDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgICBpZiAoIWxpbmUgfHwgdHlwZW9mIGxpbmUgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgICB9CgogICAgICAgICAgLy8g6KeE6IyD77ya6YCJ6aG55qC85byP77yIQTrvvInvvIzlrZfmr43lj6/ku6XkuLpB5YiwWueahOS7u+aEj+Wkp+Wwj+WGmeWtl+avje+8jOWGkuWPt+WPr+S7peabv+aNouS4uiI677ya44CBLu+8jiLlhbbkuK3kuYvkuIAKICAgICAgICAgIGNvbnN0IG9wdGlvbk1hdGNoID0gbGluZS5tYXRjaCgvXihbQS1aYS16XSlbLjrvvJrvvI7jgIFdXHMqKC4qKS8pCiAgICAgICAgICBpZiAob3B0aW9uTWF0Y2gpIHsKICAgICAgICAgICAgY29uc3Qgb3B0aW9uS2V5ID0gb3B0aW9uTWF0Y2hbMV0udG9VcHBlckNhc2UoKQogICAgICAgICAgICBjb25zdCBvcHRpb25Db250ZW50ID0gb3B0aW9uTWF0Y2hbMl0gPyBvcHRpb25NYXRjaFsyXS50cmltKCkgOiAnJwoKICAgICAgICAgICAgaWYgKG9wdGlvbktleSAmJiBvcHRpb25Db250ZW50KSB7CiAgICAgICAgICAgICAgb3B0aW9ucy5wdXNoKHsKICAgICAgICAgICAgICAgIG9wdGlvbktleTogb3B0aW9uS2V5LAogICAgICAgICAgICAgICAgbGFiZWw6IG9wdGlvbktleSwKICAgICAgICAgICAgICAgIG9wdGlvbkNvbnRlbnQ6IG9wdGlvbkNvbnRlbnQsCiAgICAgICAgICAgICAgICBjb250ZW50OiBvcHRpb25Db250ZW50CiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgfQogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmlzQW5zd2VyTGluZShsaW5lKSB8fCB0aGlzLmlzRXhwbGFuYXRpb25MaW5lKGxpbmUpIHx8IHRoaXMuaXNEaWZmaWN1bHR5TGluZShsaW5lKSkgewogICAgICAgICAgICAvLyDpgYfliLDnrZTmoYjjgIHop6PmnpDmiJbpmr7luqbooYzvvIzlgZzmraLop6PmnpDpgInpobkKICAgICAgICAgICAgYnJlYWsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIOinhOiMg++8mumAiemhueS4jumAiemhueS5i+mXtO+8jOWPr+S7peaNouihjO+8jOS5n+WPr+S7peWcqOWQjOS4gOihjAogICAgICAgICAgICAvLyDlpoLmnpzpgInpobnlnKjlkIzkuIDooYzvvIzpgInpobnkuYvpl7Toh7PlsJHpnIDopoHmnInkuIDkuKrnqbrmoLwKICAgICAgICAgICAgY29uc3QgbXVsdGlwbGVPcHRpb25zTWF0Y2ggPSBsaW5lLm1hdGNoKC8oW0EtWmEtel1bLjrvvJrvvI7jgIFdXHMqW15cc10rKD86XHMrW0EtWmEtel1bLjrvvJrvvI7jgIFdXHMqW15cc10rKSopL2cpCiAgICAgICAgICAgIGlmIChtdWx0aXBsZU9wdGlvbnNNYXRjaCkgewogICAgICAgICAgICAgIC8vIOWkhOeQhuWQjOS4gOihjOWkmuS4qumAiemhueeahOaDheWGtQogICAgICAgICAgICAgIGNvbnN0IHNpbmdsZU9wdGlvbnMgPSBsaW5lLnNwbGl0KC9ccysoPz1bQS1aYS16XVsuOu+8mu+8juOAgV0pLykKICAgICAgICAgICAgICBmb3IgKGNvbnN0IHNpbmdsZU9wdGlvbiBvZiBzaW5nbGVPcHRpb25zKSB7CiAgICAgICAgICAgICAgICBpZiAoIXNpbmdsZU9wdGlvbikgY29udGludWUKCiAgICAgICAgICAgICAgICBjb25zdCBtYXRjaCA9IHNpbmdsZU9wdGlvbi5tYXRjaCgvXihbQS1aYS16XSlbLjrvvJrvvI7jgIFdXHMqKC4qKS8pCiAgICAgICAgICAgICAgICBpZiAobWF0Y2gpIHsKICAgICAgICAgICAgICAgICAgY29uc3Qgb3B0aW9uS2V5ID0gbWF0Y2hbMV0udG9VcHBlckNhc2UoKQogICAgICAgICAgICAgICAgICBjb25zdCBvcHRpb25Db250ZW50ID0gbWF0Y2hbMl0gPyBtYXRjaFsyXS50cmltKCkgOiAnJwoKICAgICAgICAgICAgICAgICAgaWYgKG9wdGlvbktleSAmJiBvcHRpb25Db250ZW50KSB7CiAgICAgICAgICAgICAgICAgICAgb3B0aW9ucy5wdXNoKHsKICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbktleTogb3B0aW9uS2V5LAogICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IG9wdGlvbktleSwKICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbkNvbnRlbnQ6IG9wdGlvbkNvbnRlbnQsCiAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiBvcHRpb25Db250ZW50CiAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6Kej5p6Q6YCJ6aG55pe25Ye66ZSZOicsIGVycm9yKQogICAgICB9CgogICAgICByZXR1cm4geyBvcHRpb25zIH0KICAgIH0sCgogICAgLy8g5LuO6KGM5pWw57uE6Kej5p6Q6aKY55uu5YWD5L+h5oGvIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBwYXJzZVF1ZXN0aW9uTWV0YUZyb21MaW5lcyhsaW5lcywgcXVlc3Rpb24pIHsKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXQoKICAgICAgICAvLyDop4TojIPvvJrmmL7lvI/moIfms6jmoLzlvI/vvIjnrZTmoYjvvJrvvInvvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLogIjrvvJrjgIEi5YW25Lit5LmL5LiACiAgICAgICAgY29uc3QgYW5zd2VyTWF0Y2ggPSBsaW5lLm1hdGNoKC9e562U5qGIWy4677ya44CBXVxzKiguKykvKQogICAgICAgIGlmIChhbnN3ZXJNYXRjaCkgewogICAgICAgICAgcXVlc3Rpb24uY29ycmVjdEFuc3dlciA9IHRoaXMucGFyc2VBbnN3ZXJWYWx1ZShhbnN3ZXJNYXRjaFsxXSwgcXVlc3Rpb24ucXVlc3Rpb25UeXBlKQogICAgICAgICAgY29udGludWUKICAgICAgICB9CgogICAgICAgIC8vIOinhOiMg++8muino+aekOagvOW8j++8iOino+aekO+8mu+8ie+8jOWGkuWPt+WPr+S7peabv+aNouS4uiAiOu+8muOAgSLlhbbkuK3kuYvkuIAKICAgICAgICBjb25zdCBleHBsYW5hdGlvbk1hdGNoID0gbGluZS5tYXRjaCgvXuino+aekFsuOu+8muOAgV1ccyooLispLykKICAgICAgICBpZiAoZXhwbGFuYXRpb25NYXRjaCkgewogICAgICAgICAgcXVlc3Rpb24uZXhwbGFuYXRpb24gPSBleHBsYW5hdGlvbk1hdGNoWzFdLnRyaW0oKQogICAgICAgICAgY29udGludWUKICAgICAgICB9CgogICAgICAgIC8vIOinhOiMg++8mumavuW6puagvOW8j++8iOmavuW6pu+8mu+8ie+8jOWPquaUr+aMgeeugOWNleOAgeS4reetieOAgeWbsOmavuS4ieS4que6p+WIqwogICAgICAgIGNvbnN0IGRpZmZpY3VsdHlNYXRjaCA9IGxpbmUubWF0Y2goL17pmr7luqZbLjrvvJrjgIFdXHMqKOeugOWNlXzkuK3nrYl85Zuw6Zq+fOS4rSkvKQogICAgICAgIGlmIChkaWZmaWN1bHR5TWF0Y2gpIHsKICAgICAgICAgIGxldCBkaWZmaWN1bHR5ID0gZGlmZmljdWx0eU1hdGNoWzFdCiAgICAgICAgICAvLyDmoIflh4bljJbpmr7luqblgLzvvJrlsIYi5LitIue7n+S4gOS4uiLkuK3nrYkiCiAgICAgICAgICBpZiAoZGlmZmljdWx0eSA9PT0gJ+S4rScpIHsKICAgICAgICAgICAgZGlmZmljdWx0eSA9ICfkuK3nrYknCiAgICAgICAgICB9CiAgICAgICAgICAvLyDlj6rmjqXlj5fmoIflh4bnmoTkuInkuKrpmr7luqbnuqfliKsKICAgICAgICAgIGlmIChbJ+eugOWNlScsICfkuK3nrYknLCAn5Zuw6Zq+J10uaW5jbHVkZXMoZGlmZmljdWx0eSkpIHsKICAgICAgICAgICAgcXVlc3Rpb24uZGlmZmljdWx0eSA9IGRpZmZpY3VsdHkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIOS4jeaUr+aMgeeahOmavuW6pue6p+WIqzonLCBkaWZmaWN1bHR5LCAn77yM5bey5b+955WlJykKICAgICAgICAgIH0KICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDop4TojIPvvJrnrZTmoYjmlK/mjIHnm7TmjqXlnKjpopjlubLkuK3moIfms6jvvIzkvJjlhYjku6XmmL7lvI/moIfms6jnmoTnrZTmoYjkuLrlh4YKICAgICAgLy8g5aaC5p6c5rKh5pyJ5om+5Yiw5pi+5byP562U5qGI77yM5bCd6K+V5LuO6aKY55uu5YaF5a655Lit5o+Q5Y+WCiAgICAgIGlmICghcXVlc3Rpb24uY29ycmVjdEFuc3dlcikgewogICAgICAgIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIgPSB0aGlzLmV4dHJhY3RBbnN3ZXJGcm9tUXVlc3Rpb25Db250ZW50KHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCwgcXVlc3Rpb24ucXVlc3Rpb25UeXBlKQogICAgICB9CiAgICB9LAoKICAgIC8vIOS7jumimOW5suS4reaPkOWPluetlOahiCAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgZXh0cmFjdEFuc3dlckZyb21RdWVzdGlvbkNvbnRlbnQocXVlc3Rpb25Db250ZW50LCBxdWVzdGlvblR5cGUpIHsKICAgICAgaWYgKCFxdWVzdGlvbkNvbnRlbnQgfHwgdHlwZW9mIHF1ZXN0aW9uQ29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICAvLyDop4TojIPvvJrpopjlubLkuK3moLzlvI/vvIjjgJBB44CR77yJ77yM5ous5Y+35Y+v5Lul5pu/5o2i5Li65Lit6Iux5paH55qE5bCP5ous5Y+35oiW6ICF5Lit5ous5Y+3CiAgICAgICAgY29uc3QgcGF0dGVybnMgPSBbCiAgICAgICAgICAv44CQKFte44CRXSsp44CRL2csICAgIC8vIOS4reaWh+aWueaLrOWPtwogICAgICAgICAgL1xbKFteXF1dKylcXS9nLCAgIC8vIOiLseaWh+aWueaLrOWPtwogICAgICAgICAgL++8iChbXu+8iV0rKe+8iS9nLCAgICAvLyDkuK3mloflnIbmi6zlj7cKICAgICAgICAgIC9cKChbXildKylcKS9nICAgICAvLyDoi7HmloflnIbmi6zlj7cKICAgICAgICBdCgogICAgICAgIGZvciAoY29uc3QgcGF0dGVybiBvZiBwYXR0ZXJucykgewogICAgICAgICAgY29uc3QgbWF0Y2hlcyA9IHF1ZXN0aW9uQ29udGVudC5tYXRjaChwYXR0ZXJuKQogICAgICAgICAgaWYgKG1hdGNoZXMgJiYgbWF0Y2hlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIC8vIOaPkOWPluacgOWQjuS4gOS4quWMuemFjemhueS9nOS4uuetlOahiO+8iOmAmuW4uOetlOahiOWcqOmimOebruacq+Wwvu+8iQogICAgICAgICAgICBjb25zdCBsYXN0TWF0Y2ggPSBtYXRjaGVzW21hdGNoZXMubGVuZ3RoIC0gMV0KICAgICAgICAgICAgY29uc3QgYW5zd2VyID0gbGFzdE1hdGNoLnJlcGxhY2UoL1vjgJDjgJFcW1xd77yI77yJKCldL2csICcnKS50cmltKCkKCiAgICAgICAgICAgIGlmIChhbnN3ZXIpIHsKICAgICAgICAgICAgICByZXR1cm4gdGhpcy5wYXJzZUFuc3dlclZhbHVlKGFuc3dlciwgcXVlc3Rpb25UeXBlKQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDku47popjlubLmj5Dlj5bnrZTmoYjml7blh7rplJk6JywgZXJyb3IpCiAgICAgIH0KCiAgICAgIHJldHVybiAnJwogICAgfSwKCiAgICAvLyDop6PmnpDnrZTmoYjlgLwKICAgIHBhcnNlQW5zd2VyVmFsdWUoYW5zd2VyVGV4dCwgcXVlc3Rpb25UeXBlKSB7CiAgICAgIGlmICghYW5zd2VyVGV4dCB8fCB0eXBlb2YgYW5zd2VyVGV4dCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCB0cmltbWVkQW5zd2VyID0gYW5zd2VyVGV4dC50cmltKCkKCiAgICAgICAgaWYgKCF0cmltbWVkQW5zd2VyKSB7CiAgICAgICAgICByZXR1cm4gJycKICAgICAgICB9CgogICAgICAgIGlmIChxdWVzdGlvblR5cGUgPT09ICdqdWRnbWVudCcpIHsKICAgICAgICAgIC8vIOWIpOaWremimOetlOahiOWkhOeQhiAtIOS/neaMgeWOn+Wni+agvOW8j++8jOS4jei9rOaNouS4unRydWUvZmFsc2UKICAgICAgICAgIHJldHVybiB0cmltbWVkQW5zd2VyCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOmAieaLqemimOetlOahiOWkhOeQhgogICAgICAgICAgcmV0dXJuIHRyaW1tZWRBbnN3ZXIudG9VcHBlckNhc2UoKQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6Kej5p6Q562U5qGI5YC85pe25Ye66ZSZOicsIGVycm9yKQogICAgICAgIHJldHVybiBhbnN3ZXJUZXh0IHx8ICcnCiAgICAgIH0KICAgIH0sCgogICAgLy8g5oyJ6aKY5Z6L5YiG5Ymy5YaF5a65CiAgICBzcGxpdEJ5UXVlc3Rpb25UeXBlKGNvbnRlbnQpIHsKICAgICAgY29uc3Qgc2VjdGlvbnMgPSBbXQogICAgICBjb25zdCB0eXBlUmVnZXggPSAvXFso5Y2V6YCJ6aKYfOWkmumAiemimHzliKTmlq3popgpXF0vZwoKICAgICAgbGV0IGxhc3RJbmRleCA9IDAKICAgICAgbGV0IG1hdGNoCiAgICAgIGxldCBjdXJyZW50VHlwZSA9IG51bGwKCiAgICAgIHdoaWxlICgobWF0Y2ggPSB0eXBlUmVnZXguZXhlYyhjb250ZW50KSkgIT09IG51bGwpIHsKICAgICAgICBpZiAoY3VycmVudFR5cGUpIHsKICAgICAgICAgIC8vIOS/neWtmOS4iuS4gOS4quWMuuWfnwogICAgICAgICAgc2VjdGlvbnMucHVzaCh7CiAgICAgICAgICAgIHR5cGU6IGN1cnJlbnRUeXBlLAogICAgICAgICAgICBjb250ZW50OiBjb250ZW50LnN1YnN0cmluZyhsYXN0SW5kZXgsIG1hdGNoLmluZGV4KS50cmltKCkKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICAgIGN1cnJlbnRUeXBlID0gbWF0Y2hbMV0KICAgICAgICBsYXN0SW5kZXggPSBtYXRjaC5pbmRleCArIG1hdGNoWzBdLmxlbmd0aAogICAgICB9CgogICAgICAvLyDkv53lrZjmnIDlkI7kuIDkuKrljLrln58KICAgICAgaWYgKGN1cnJlbnRUeXBlKSB7CiAgICAgICAgc2VjdGlvbnMucHVzaCh7CiAgICAgICAgICB0eXBlOiBjdXJyZW50VHlwZSwKICAgICAgICAgIGNvbnRlbnQ6IGNvbnRlbnQuc3Vic3RyaW5nKGxhc3RJbmRleCkudHJpbSgpCiAgICAgICAgfSkKICAgICAgfQoKICAgICAgcmV0dXJuIHNlY3Rpb25zCiAgICB9LAoKICAgIC8vIOino+aekOWMuuWfn+WGheeahOmimOebrgogICAgcGFyc2VTZWN0aW9uUXVlc3Rpb25zKHNlY3Rpb24pIHsKICAgICAgY29uc3QgcXVlc3Rpb25zID0gW10KICAgICAgY29uc3QgcXVlc3Rpb25UeXBlID0gdGhpcy5jb252ZXJ0UXVlc3Rpb25UeXBlKHNlY3Rpb24udHlwZSkKCiAgICAgIC8vIOaMiemimOWPt+WIhuWJsumimOebrgogICAgICBjb25zdCBxdWVzdGlvbkJsb2NrcyA9IHRoaXMuc3BsaXRCeVF1ZXN0aW9uTnVtYmVyKHNlY3Rpb24uY29udGVudCkKCiAgICAgIHF1ZXN0aW9uQmxvY2tzLmZvckVhY2goKGJsb2NrLCBpbmRleCkgPT4gewogICAgICAgIHRyeSB7CiAgICAgICAgICBjb25zdCBxdWVzdGlvbiA9IHRoaXMucGFyc2VRdWVzdGlvbkJsb2NrKGJsb2NrLCBxdWVzdGlvblR5cGUsIGluZGV4ICsgMSkKICAgICAgICAgIGlmIChxdWVzdGlvbikgewogICAgICAgICAgICBxdWVzdGlvbnMucHVzaChxdWVzdGlvbikKICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGDnrKwke2luZGV4ICsgMX3popjop6PmnpDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQogICAgICAgIH0KICAgICAgfSkKCiAgICAgIHJldHVybiBxdWVzdGlvbnMKICAgIH0sCgogICAgLy8g5oyJ6aKY5Y+35YiG5Ymy6aKY55uuCiAgICBzcGxpdEJ5UXVlc3Rpb25OdW1iZXIoY29udGVudCkgewogICAgICBjb25zdCBibG9ja3MgPSBbXQogICAgICBjb25zdCBudW1iZXJSZWdleCA9IC9eXHMqKFxkKylbLjrvvJrvvI5dXHMqL2dtCgogICAgICBsZXQgbGFzdEluZGV4ID0gMAogICAgICBsZXQgbWF0Y2gKCiAgICAgIHdoaWxlICgobWF0Y2ggPSBudW1iZXJSZWdleC5leGVjKGNvbnRlbnQpKSAhPT0gbnVsbCkgewogICAgICAgIGlmIChsYXN0SW5kZXggPiAwKSB7CiAgICAgICAgICAvLyDkv53lrZjkuIrkuIDpopgKICAgICAgICAgIGJsb2Nrcy5wdXNoKGNvbnRlbnQuc3Vic3RyaW5nKGxhc3RJbmRleCwgbWF0Y2guaW5kZXgpLnRyaW0oKSkKICAgICAgICB9CiAgICAgICAgbGFzdEluZGV4ID0gbWF0Y2guaW5kZXgKICAgICAgfQoKICAgICAgLy8g5L+d5a2Y5pyA5ZCO5LiA6aKYCiAgICAgIGlmIChsYXN0SW5kZXggPCBjb250ZW50Lmxlbmd0aCkgewogICAgICAgIGJsb2Nrcy5wdXNoKGNvbnRlbnQuc3Vic3RyaW5nKGxhc3RJbmRleCkudHJpbSgpKQogICAgICB9CgogICAgICByZXR1cm4gYmxvY2tzLmZpbHRlcihibG9jayA9PiBibG9jay5sZW5ndGggPiAwKQogICAgfSwKCiAgICAvLyDop6PmnpDljZXkuKrpopjnm67lnZcKICAgIHBhcnNlUXVlc3Rpb25CbG9jayhibG9jaywgcXVlc3Rpb25UeXBlKSB7CiAgICAgIGNvbnN0IGxpbmVzID0gYmxvY2suc3BsaXQoJ1xuJykubWFwKGxpbmUgPT4gbGluZS50cmltKCkpLmZpbHRlcihsaW5lID0+IGxpbmUubGVuZ3RoID4gMCkKCiAgICAgIGlmIChsaW5lcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+mimOebruWGheWuueS4uuepuicpCiAgICAgIH0KCiAgICAgIC8vIOaPkOWPlumimOW5su+8iOenu+mZpOmimOWPt++8iQogICAgICBjb25zdCBmaXJzdExpbmUgPSBsaW5lc1swXQogICAgICBsZXQgcXVlc3Rpb25Db250ZW50ID0gJycKICAgICAgbGV0IGN1cnJlbnRMaW5lSW5kZXggPSAwCgogICAgICAvLyDlpoLmnpznrKzkuIDooYzljIXlkKvpopjlj7fvvIznp7vpmaTpopjlj7fpg6jliIYKICAgICAgY29uc3QgbnVtYmVyTWF0Y2ggPSBmaXJzdExpbmUubWF0Y2goL15ccyooXGQrKVsuOu+8mu+8juOAgV1ccyooLiopLykKICAgICAgaWYgKG51bWJlck1hdGNoKSB7CiAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gbnVtYmVyTWF0Y2hbMl0udHJpbSgpIC8vIOenu+mZpOmimOWPt++8jOWPquS/neeVmemimOW5sgogICAgICAgIGN1cnJlbnRMaW5lSW5kZXggPSAxCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5aaC5p6c56ys5LiA6KGM5LiN5YyF5ZCr6aKY5Y+377yM55u05o6l5L2c5Li66aKY5bmy77yM5L2G5LuN6ZyA5riF55CG5Y+v6IO955qE6aKY5Y+3CiAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gdGhpcy5yZW1vdmVRdWVzdGlvbk51bWJlcihmaXJzdExpbmUpLnRyaW0oKQogICAgICAgIGN1cnJlbnRMaW5lSW5kZXggPSAxCiAgICAgIH0KCiAgICAgIC8vIOe7p+e7reivu+WPlumimOW5suWGheWuue+8iOebtOWIsOmBh+WIsOmAiemhue+8iQogICAgICB3aGlsZSAoY3VycmVudExpbmVJbmRleCA8IGxpbmVzLmxlbmd0aCkgewogICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tjdXJyZW50TGluZUluZGV4XQogICAgICAgIGlmICh0aGlzLmlzT3B0aW9uTGluZShsaW5lKSkgewogICAgICAgICAgYnJlYWsKICAgICAgICB9CiAgICAgICAgcXVlc3Rpb25Db250ZW50ICs9ICdcbicgKyBsaW5lCiAgICAgICAgY3VycmVudExpbmVJbmRleCsrCiAgICAgIH0KCiAgICAgIGNvbnN0IHF1ZXN0aW9uID0gewogICAgICAgIHF1ZXN0aW9uVHlwZTogcXVlc3Rpb25UeXBlLAogICAgICAgIHF1ZXN0aW9uQ29udGVudDogcXVlc3Rpb25Db250ZW50LnRyaW0oKSwKICAgICAgICBkaWZmaWN1bHR5OiAnJywgLy8g5LiN6K6+572u6buY6K6k5YC8CiAgICAgICAgZXhwbGFuYXRpb246ICcnLAogICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgIGNvcnJlY3RBbnN3ZXI6ICcnCiAgICAgIH0KCiAgICAgIC8vIOino+aekOmAiemhue+8iOWvueS6jumAieaLqemimO+8iQogICAgICBpZiAocXVlc3Rpb25UeXBlICE9PSAnanVkZ21lbnQnKSB7CiAgICAgICAgY29uc3Qgb3B0aW9uUmVzdWx0ID0gdGhpcy5wYXJzZU9wdGlvbnMobGluZXMsIGN1cnJlbnRMaW5lSW5kZXgpCiAgICAgICAgcXVlc3Rpb24ub3B0aW9ucyA9IG9wdGlvblJlc3VsdC5vcHRpb25zCiAgICAgICAgY3VycmVudExpbmVJbmRleCA9IG9wdGlvblJlc3VsdC5uZXh0SW5kZXgKICAgICAgfQoKICAgICAgLy8g6Kej5p6Q562U5qGI44CB6Kej5p6Q44CB6Zq+5bqmCiAgICAgIHRoaXMucGFyc2VRdWVzdGlvbk1ldGEobGluZXMsIGN1cnJlbnRMaW5lSW5kZXgsIHF1ZXN0aW9uKQoKICAgICAgLy8g5pyA57uI5riF55CG77ya56Gu5L+d6aKY55uu5YaF5a655a6M5YWo5rKh5pyJ6aKY5Y+3CiAgICAgIHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCA9IHRoaXMucmVtb3ZlUXVlc3Rpb25OdW1iZXIocXVlc3Rpb24ucXVlc3Rpb25Db250ZW50KQoKICAgICAgcmV0dXJuIHF1ZXN0aW9uCiAgICB9LAoKICAgIC8vIOWIpOaWreaYr+WQpuS4uumAiemhueihjAogICAgaXNPcHRpb25MaW5lKGxpbmUpIHsKICAgICAgcmV0dXJuIC9eW0EtWmEtel1bLjrvvJrvvI5dXHMqLy50ZXN0KGxpbmUpCiAgICB9LAoKICAgIC8vIOino+aekOmAiemhuQogICAgcGFyc2VPcHRpb25zKGxpbmVzLCBzdGFydEluZGV4KSB7CiAgICAgIGNvbnN0IG9wdGlvbnMgPSBbXQogICAgICBsZXQgY3VycmVudEluZGV4ID0gc3RhcnRJbmRleAoKICAgICAgd2hpbGUgKGN1cnJlbnRJbmRleCA8IGxpbmVzLmxlbmd0aCkgewogICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tjdXJyZW50SW5kZXhdCiAgICAgICAgY29uc3Qgb3B0aW9uTWF0Y2ggPSBsaW5lLm1hdGNoKC9eKFtBLVphLXpdKVsuOu+8mu+8jl1ccyooLiopLykKCiAgICAgICAgaWYgKCFvcHRpb25NYXRjaCkgewogICAgICAgICAgYnJlYWsKICAgICAgICB9CgogICAgICAgIG9wdGlvbnMucHVzaCh7CiAgICAgICAgICBvcHRpb25LZXk6IG9wdGlvbk1hdGNoWzFdLnRvVXBwZXJDYXNlKCksCiAgICAgICAgICBvcHRpb25Db250ZW50OiBvcHRpb25NYXRjaFsyXS50cmltKCkKICAgICAgICB9KQoKICAgICAgICBjdXJyZW50SW5kZXgrKwogICAgICB9CgogICAgICByZXR1cm4geyBvcHRpb25zLCBuZXh0SW5kZXg6IGN1cnJlbnRJbmRleCB9CiAgICB9LAoKICAgIC8vIOino+aekOmimOebruWFg+S/oeaBr++8iOetlOahiOOAgeino+aekOOAgemavuW6pu+8iQogICAgcGFyc2VRdWVzdGlvbk1ldGEobGluZXMsIHN0YXJ0SW5kZXgsIHF1ZXN0aW9uKSB7CiAgICAgIGZvciAobGV0IGkgPSBzdGFydEluZGV4OyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgLy8g6Kej5p6Q562U5qGICiAgICAgICAgY29uc3QgYW5zd2VyTWF0Y2ggPSBsaW5lLm1hdGNoKC9e562U5qGIW++8mjpdXHMqKC4rKS8pCiAgICAgICAgaWYgKGFuc3dlck1hdGNoKSB7CiAgICAgICAgICBxdWVzdGlvbi5jb3JyZWN0QW5zd2VyID0gdGhpcy5wYXJzZUFuc3dlcihhbnN3ZXJNYXRjaFsxXSwgcXVlc3Rpb24ucXVlc3Rpb25UeXBlKQogICAgICAgICAgY29udGludWUKICAgICAgICB9CgogICAgICAgIC8vIOino+aekOino+aekAogICAgICAgIGNvbnN0IGV4cGxhbmF0aW9uTWF0Y2ggPSBsaW5lLm1hdGNoKC9e6Kej5p6QW++8mjpdXHMqKC4rKS8pCiAgICAgICAgaWYgKGV4cGxhbmF0aW9uTWF0Y2gpIHsKICAgICAgICAgIHF1ZXN0aW9uLmV4cGxhbmF0aW9uID0gZXhwbGFuYXRpb25NYXRjaFsxXS50cmltKCkKICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQoKICAgICAgICAvLyDop6PmnpDpmr7luqYgLSDlj6rmlK/mjIHnroDljZXjgIHkuK3nrYnjgIHlm7Dpmr7kuInkuKrnuqfliKsKICAgICAgICBjb25zdCBkaWZmaWN1bHR5TWF0Y2ggPSBsaW5lLm1hdGNoKC9e6Zq+5bqmW++8mjpdXHMqKOeugOWNlXzkuK3nrYl85Zuw6Zq+fOS4rSkvKQogICAgICAgIGlmIChkaWZmaWN1bHR5TWF0Y2gpIHsKICAgICAgICAgIGxldCBkaWZmaWN1bHR5ID0gZGlmZmljdWx0eU1hdGNoWzFdCiAgICAgICAgICAvLyDmoIflh4bljJbpmr7luqblgLzvvJrlsIYi5LitIue7n+S4gOS4uiLkuK3nrYkiCiAgICAgICAgICBpZiAoZGlmZmljdWx0eSA9PT0gJ+S4rScpIHsKICAgICAgICAgICAgZGlmZmljdWx0eSA9ICfkuK3nrYknCiAgICAgICAgICB9CiAgICAgICAgICAvLyDlj6rmjqXlj5fmoIflh4bnmoTkuInkuKrpmr7luqbnuqfliKsKICAgICAgICAgIGlmIChbJ+eugOWNlScsICfkuK3nrYknLCAn5Zuw6Zq+J10uaW5jbHVkZXMoZGlmZmljdWx0eSkpIHsKICAgICAgICAgICAgcXVlc3Rpb24uZGlmZmljdWx0eSA9IGRpZmZpY3VsdHkKICAgICAgICAgIH0KICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDlpoLmnpzmsqHmnInmmL7lvI/nrZTmoYjvvIzlsJ3or5Xku47popjlubLkuK3mj5Dlj5YKICAgICAgaWYgKCFxdWVzdGlvbi5jb3JyZWN0QW5zd2VyKSB7CiAgICAgICAgcXVlc3Rpb24uY29ycmVjdEFuc3dlciA9IHRoaXMuZXh0cmFjdEFuc3dlckZyb21Db250ZW50KHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCwgcXVlc3Rpb24ucXVlc3Rpb25UeXBlKQogICAgICB9CiAgICB9LAoKCgogICAgLy8g5LuO6aKY5bmy5Lit5o+Q5Y+W562U5qGICiAgICBleHRyYWN0QW5zd2VyRnJvbUNvbnRlbnQoY29udGVudCwgcXVlc3Rpb25UeXBlKSB7CiAgICAgIC8vIOaUr+aMgeeahOaLrOWPt+exu+WeiwogICAgICBjb25zdCBicmFja2V0UGF0dGVybnMgPSBbCiAgICAgICAgL+OAkChbXuOAkV0rKeOAkS9nLAogICAgICAgIC9cWyhbXlxdXSspXF0vZywKICAgICAgICAv77yIKFte77yJXSsp77yJL2csCiAgICAgICAgL1woKFteKV0rKVwpL2cKICAgICAgXQoKICAgICAgZm9yIChjb25zdCBwYXR0ZXJuIG9mIGJyYWNrZXRQYXR0ZXJucykgewogICAgICAgIGNvbnN0IG1hdGNoZXMgPSBbLi4uY29udGVudC5tYXRjaEFsbChwYXR0ZXJuKV0KICAgICAgICBpZiAobWF0Y2hlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICBjb25zdCBhbnN3ZXIgPSBtYXRjaGVzW21hdGNoZXMubGVuZ3RoIC0gMV1bMV0gLy8g5Y+W5pyA5ZCO5LiA5Liq5Yy56YWNCiAgICAgICAgICByZXR1cm4gdGhpcy5wYXJzZUFuc3dlcihhbnN3ZXIsIHF1ZXN0aW9uVHlwZSkKICAgICAgICB9CiAgICAgIH0KCiAgICAgIHJldHVybiAnJwogICAgfSwKCiAgICAvLyDovazmjaLpopjlnosKICAgIGNvbnZlcnRRdWVzdGlvblR5cGUodHlwZVRleHQpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAn5Y2V6YCJ6aKYJzogJ3NpbmdsZScsCiAgICAgICAgJ+WkmumAiemimCc6ICdtdWx0aXBsZScsCiAgICAgICAgJ+WIpOaWremimCc6ICdqdWRnbWVudCcKICAgICAgfQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlVGV4dF0gfHwgJ3NpbmdsZScKICAgIH0sCgogICAgLy8g6I635Y+W6aKY5Z6L5ZCN56ewCiAgICBnZXRRdWVzdGlvblR5cGVOYW1lKHR5cGUpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAnc2luZ2xlJzogJ+WNlemAiemimCcsCiAgICAgICAgJ211bHRpcGxlJzogJ+WkmumAiemimCcsCiAgICAgICAgJ2p1ZGdtZW50JzogJ+WIpOaWremimCcKICAgICAgfQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCAn5pyq55+lJwogICAgfSwKCiAgICAvLyDojrflj5bpopjlnovpopzoibIKICAgIGdldFF1ZXN0aW9uVHlwZUNvbG9yKHR5cGUpIHsKICAgICAgY29uc3QgY29sb3JNYXAgPSB7CiAgICAgICAgJ3NpbmdsZSc6ICdwcmltYXJ5JywKICAgICAgICAnbXVsdGlwbGUnOiAnc3VjY2VzcycsCiAgICAgICAgJ2p1ZGdtZW50JzogJ3dhcm5pbmcnCiAgICAgIH0KICAgICAgcmV0dXJuIGNvbG9yTWFwW3R5cGVdIHx8ICdpbmZvJwogICAgfSwKCiAgICAvLyA9PT09PT09PT09PT09PT09PT09PSDnvJPlrZjkv53lrZjnm7jlhbPmlrnms5UgPT09PT09PT09PT09PT09PT09PT0KCiAgICAvLyDliqDovb3nvJPlrZjnmoTlhoXlrrkKICAgIGxvYWRDYWNoZWRDb250ZW50KCkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IGNhY2hlZERhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSh0aGlzLmNhY2hlS2V5KQogICAgICAgIGlmIChjYWNoZWREYXRhKSB7CiAgICAgICAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZShjYWNoZWREYXRhKQoKICAgICAgICAgIC8vIOaBouWkjeWGheWuuQogICAgICAgICAgdGhpcy5kb2N1bWVudENvbnRlbnQgPSBkYXRhLmRvY3VtZW50Q29udGVudCB8fCAnJwogICAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gZGF0YS5kb2N1bWVudEh0bWxDb250ZW50IHx8ICcnCiAgICAgICAgICB0aGlzLmxhc3RTYXZlVGltZSA9IGRhdGEubGFzdFNhdmVUaW1lIHx8ICcnCgogICAgICAgICAgLy8g5aaC5p6c5pyJ5YaF5a6577yM5qCH6K6w5Li65pyJ5pyq5L+d5a2Y55qE5pu05pS5CiAgICAgICAgICBpZiAodGhpcy5kb2N1bWVudENvbnRlbnQgfHwgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50KSB7CiAgICAgICAgICAgIHRoaXMuaGFzVW5zYXZlZENoYW5nZXMgPSB0cnVlCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliqDovb3nvJPlrZjlhoXlrrnlpLHotKU6JywgZXJyb3IpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5L+d5a2Y5YaF5a655Yiw57yT5a2YCiAgICBzYXZlVG9DYWNoZSgpIHsKICAgICAgLy8g6Ziy5oqW5L+d5a2Y77yM6YG/5YWN6aKR57mB5YaZ5YWlCiAgICAgIGlmICh0aGlzLmF1dG9TYXZlVGltZXIpIHsKICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5hdXRvU2F2ZVRpbWVyKQogICAgICB9CgogICAgICB0aGlzLmF1dG9TYXZlVGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICB0aGlzLnNhdmVUb0NhY2hlTm93KCkKICAgICAgfSwgMjAwMCkgLy8gMuenkuWQjuS/neWtmAogICAgfSwKCiAgICAvLyDnq4vljbPkv53lrZjliLDnvJPlrZgKICAgIHNhdmVUb0NhY2hlTm93KCkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IGRhdGFUb1NhdmUgPSB7CiAgICAgICAgICBkb2N1bWVudENvbnRlbnQ6IHRoaXMuZG9jdW1lbnRDb250ZW50IHx8ICcnLAogICAgICAgICAgZG9jdW1lbnRIdG1sQ29udGVudDogdGhpcy5kb2N1bWVudEh0bWxDb250ZW50IHx8ICcnLAogICAgICAgICAgbGFzdFNhdmVUaW1lOiB0aGlzLmxhc3RTYXZlVGltZSB8fCBuZXcgRGF0ZSgpLnRvTG9jYWxlU3RyaW5nKCksCiAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCkKICAgICAgICB9CgogICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKHRoaXMuY2FjaGVLZXksIEpTT04uc3RyaW5naWZ5KGRhdGFUb1NhdmUpKQogICAgICAgIHRoaXMuaGFzVW5zYXZlZENoYW5nZXMgPSBmYWxzZQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDkv53lrZjliLDnvJPlrZjlpLHotKU6JywgZXJyb3IpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5riF6Zmk57yT5a2YCiAgICBjbGVhckNhY2hlKCkgewogICAgICB0cnkgewogICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKHRoaXMuY2FjaGVLZXkpCiAgICAgICAgdGhpcy5oYXNVbnNhdmVkQ2hhbmdlcyA9IGZhbHNlCiAgICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8g57yT5a2Y5bey5riF6ZmkJykKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg5riF6Zmk57yT5a2Y5aSx6LSlOicsIGVycm9yKQogICAgICB9CiAgICB9LAoKICAgIC8vIOmhtemdouWFs+mXreWJjeeahOWkhOeQhgogICAgaGFuZGxlQmVmb3JlVW5sb2FkKGV2ZW50KSB7CiAgICAgIGlmICh0aGlzLmhhc1Vuc2F2ZWRDaGFuZ2VzKSB7CiAgICAgICAgLy8g56uL5Y2z5L+d5a2Y5Yiw57yT5a2YCiAgICAgICAgdGhpcy5zYXZlVG9DYWNoZU5vdygpCgogICAgICAgIC8vIOaPkOekuueUqOaIt+acieacquS/neWtmOeahOabtOaUuQogICAgICAgIGNvbnN0IG1lc3NhZ2UgPSAn5oKo5pyJ5pyq5L+d5a2Y55qE5pu05pS577yM56Gu5a6a6KaB56a75byA5ZCX77yfJwogICAgICAgIGV2ZW50LnJldHVyblZhbHVlID0gbWVzc2FnZQogICAgICAgIHJldHVybiBtZXNzYWdlCiAgICAgIH0KICAgIH0sCgogICAgLy8g5omL5Yqo5L+d5a2YCiAgICBtYW51YWxTYXZlKCkgewogICAgICB0aGlzLnNhdmVUb0NhY2hlTm93KCkKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflhoXlrrnlt7Lkv53lrZjliLDmnKzlnLDnvJPlrZgnKQogICAgfSwKCiAgICAvLyDlpITnkIbnvJPlrZjnm7jlhbPlkb3ku6QKICAgIGhhbmRsZUNhY2hlQ29tbWFuZChjb21tYW5kKSB7CiAgICAgIHN3aXRjaCAoY29tbWFuZCkgewogICAgICAgIGNhc2UgJ2NsZWFyJzoKICAgICAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgea4hemZpOaJgOaciee8k+WtmOeahOiNieeov+WGheWuueWQl++8nycsICfmj5DnpLonLCB7CiAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuY2xlYXJDYWNoZSgpCiAgICAgICAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gJycKICAgICAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gJycKICAgICAgICAgICAgdGhpcy5sYXN0U2F2ZVRpbWUgPSAnJwoKICAgICAgICAgICAgLy8g5riF56m657yW6L6R5Zmo5YaF5a65CiAgICAgICAgICAgIGlmICh0aGlzLnJpY2hFZGl0b3IgJiYgdGhpcy5lZGl0b3JJbml0aWFsaXplZCkgewogICAgICAgICAgICAgIHRoaXMucmljaEVkaXRvci5zZXREYXRhKCcnKQogICAgICAgICAgICB9CgogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+e8k+WtmOW3sua4hemZpCcpCiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgIC8vIOeUqOaIt+WPlua2iAogICAgICAgICAgfSkKICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAnZXhwb3J0JzoKICAgICAgICAgIHRoaXMuZXhwb3J0RHJhZnQoKQogICAgICAgICAgYnJlYWsKICAgICAgfQogICAgfSwKCiAgICAvLyDlr7zlh7rojYnnqL8KICAgIGV4cG9ydERyYWZ0KCkgewogICAgICBpZiAoIXRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCAmJiAhdGhpcy5kb2N1bWVudENvbnRlbnQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ayoeacieWPr+WvvOWHuueahOiNieeov+WGheWuuScpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIGNvbnN0IGNvbnRlbnQgPSB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgfHwgdGhpcy5kb2N1bWVudENvbnRlbnQKICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtjb250ZW50XSwgeyB0eXBlOiAndGV4dC9odG1sO2NoYXJzZXQ9dXRmLTgnIH0pCiAgICAgIGNvbnN0IHVybCA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYikKICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKQogICAgICBsaW5rLmhyZWYgPSB1cmwKICAgICAgbGluay5kb3dubG9hZCA9IGDpopjnm67ojYnnqL9fJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoLzovZywgJy0nKX0uaHRtbGAKICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKQogICAgICBsaW5rLmNsaWNrKCkKICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKQogICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVybCkKCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6I2J56i/5bey5a+85Ye6JykKICAgIH0sCgogICAgLy8gPT09PT09PT09PT09PT09PT09PT0g5Y6f5pyJ5pa55rOVID09PT09PT09PT09PT09PT09PT09CgogICAgLy8g6I635Y+W5qC85byP5YyW55qE6aKY55uu5YaF5a6577yI5pSv5oyB5a+M5paH5pys5qC85byP77yJCiAgICBnZXRGb3JtYXR0ZWRRdWVzdGlvbkNvbnRlbnQocXVlc3Rpb24pIHsKICAgICAgaWYgKCFxdWVzdGlvbiB8fCAhcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50KSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIGxldCBjb250ZW50ID0gcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50CgogICAgICAvLyDlpoLmnpzmnIlIVE1M5YaF5a655LiU5YyF5ZCr5a+M5paH5pys5qCH562+77yM5LyY5YWI5L2/55SoSFRNTOWGheWuuQogICAgICBpZiAodGhpcy5kb2N1bWVudEh0bWxDb250ZW50ICYmIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudC5pbmNsdWRlcygnPCcpKSB7CiAgICAgICAgLy8g5LuOSFRNTOWGheWuueS4reaPkOWPluWvueW6lOeahOmimOebruWGheWuuQogICAgICAgIGNvbnN0IGh0bWxDb250ZW50ID0gdGhpcy5leHRyYWN0UXVlc3Rpb25Gcm9tSHRtbChxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQsIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCkKICAgICAgICBpZiAoaHRtbENvbnRlbnQpIHsKICAgICAgICAgIGNvbnRlbnQgPSBodG1sQ29udGVudAogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5riF55CG6aKY5Y+377ya56Gu5L+d6aKY55uu5YaF5a655LiN5Lul5pWw5a2XK+espuWPt+W8gOWktAogICAgICBjb250ZW50ID0gdGhpcy5yZW1vdmVRdWVzdGlvbk51bWJlcihjb250ZW50KQoKICAgICAgcmV0dXJuIHRoaXMucHJvY2Vzc0ltYWdlUGF0aHMoY29udGVudCkKICAgIH0sCgogICAgLy8g5riF55CG6aKY55uu5YaF5a655Lit55qE6aKY5Y+3CiAgICByZW1vdmVRdWVzdGlvbk51bWJlcihjb250ZW50KSB7CiAgICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gY29udGVudAogICAgICB9CgogICAgICAvLyDlpITnkIZIVE1M5YaF5a65CiAgICAgIGlmIChjb250ZW50LmluY2x1ZGVzKCc8JykpIHsKICAgICAgICAvLyDlr7nkuo5IVE1M5YaF5a6577yM6ZyA6KaB5riF55CG5qCH562+5YaF55qE6aKY5Y+3CiAgICAgICAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSgvPHBbXj5dKj4oXHMqXGQrWy4677ya77yO44CBXVxzKikoLio/KTxcL3A+L2dpLCAnPHA+JDI8L3A+JykKICAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL14oXHMqXGQrWy4677ya77yO44CBXVxzKikvLCAnJykgLy8g5riF55CG5byA5aS055qE6aKY5Y+3CiAgICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC8+XHMqXGQrWy4677ya77yO44CBXVxzKi9nLCAnPicpIC8vIOa4heeQhuagh+etvuWQjueahOmimOWPtwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWvueS6jue6r+aWh+acrOWGheWuue+8jOebtOaOpea4heeQhuW8gOWktOeahOmimOWPtwogICAgICAgIHJldHVybiBjb250ZW50LnJlcGxhY2UoL15ccypcZCtbLjrvvJrvvI7jgIFdXHMqLywgJycpLnRyaW0oKQogICAgICB9CiAgICB9LAoKICAgIC8vIOS7jkhUTUzlhoXlrrnkuK3mj5Dlj5blr7nlupTnmoTpopjnm67lhoXlrrkKICAgIGV4dHJhY3RRdWVzdGlvbkZyb21IdG1sKHBsYWluQ29udGVudCwgaHRtbENvbnRlbnQpIHsKICAgICAgaWYgKCFwbGFpbkNvbnRlbnQgfHwgIWh0bWxDb250ZW50KSB7CiAgICAgICAgcmV0dXJuIHBsYWluQ29udGVudAogICAgICB9CgogICAgICB0cnkgewogICAgICAgIC8vIOeugOWNleeahOWMuemFjeetlueVpe+8muafpeaJvuWMheWQq+mimOebruWGheWuueeahEhUTUzmrrXokL0KICAgICAgICBjb25zdCBwbGFpblRleHQgPSBwbGFpbkNvbnRlbnQucmVwbGFjZSgvXlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpCgogICAgICAgIC8vIOWcqEhUTUzlhoXlrrnkuK3mn6Xmib7ljIXlkKvov5nkuKrmlofmnKznmoTmrrXokL0KICAgICAgICBjb25zdCBwYXJhZ3JhcGhzID0gaHRtbENvbnRlbnQubWF0Y2goLzxwW14+XSo+Lio/PFwvcD4vZ2kpIHx8IFtdCgogICAgICAgIGZvciAoY29uc3QgcGFyYWdyYXBoIG9mIHBhcmFncmFwaHMpIHsKICAgICAgICAgIGNvbnN0IHBhcmFncmFwaFRleHQgPSBwYXJhZ3JhcGgucmVwbGFjZSgvPFtePl0qPi9nLCAnJykudHJpbSgpCiAgICAgICAgICAvLyDmuIXnkIbmrrXokL3mlofmnKzkuK3nmoTpopjlj7flho3ov5vooYzljLnphY0KICAgICAgICAgIGNvbnN0IGNsZWFuUGFyYWdyYXBoVGV4dCA9IHBhcmFncmFwaFRleHQucmVwbGFjZSgvXlxzKlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpCiAgICAgICAgICBpZiAoY2xlYW5QYXJhZ3JhcGhUZXh0LmluY2x1ZGVzKHBsYWluVGV4dC5zdWJzdHJpbmcoMCwgMjApKSkgewogICAgICAgICAgICAvLyDmib7liLDljLnphY3nmoTmrrXokL3vvIzov5Tlm55IVE1M5qC85byP77yI5L2G6KaB5riF55CG6aKY5Y+377yJCiAgICAgICAgICAgIHJldHVybiB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKHBhcmFncmFwaCkKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC8vIOWmguaenOayoeacieaJvuWIsOWMuemFjeeahOauteiQve+8jOi/lOWbnuWOn+Wni+WGheWuuQogICAgICAgIHJldHVybiBwbGFpbkNvbnRlbnQKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfmj5Dlj5ZIVE1M6aKY55uu5YaF5a655aSx6LSlOicsIGVycm9yKQogICAgICAgIHJldHVybiBwbGFpbkNvbnRlbnQKICAgICAgfQogICAgfSwKCgogICAgLy8g5pCc57SiCiAgICBoYW5kbGVTZWFyY2goKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgfSwKICAgIC8vIOmHjee9ruaQnOe0ogogICAgcmVzZXRTZWFyY2goKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucXVlc3Rpb25UeXBlID0gbnVsbAogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRpZmZpY3VsdHkgPSBudWxsCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucXVlc3Rpb25Db250ZW50ID0gbnVsbAogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuhBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;;AAGA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;;AAGA;;AAEA;AACA;;;;AAIA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/biz/questionBank", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"importDrawerVisible = true\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <span class=\"orange\">最后保存的草稿时间：{{ lastSaveTime || '暂无' }}</span>\n            <span v-if=\"hasUnsavedChanges\" class=\"unsaved-indicator\">●</span>\n            <div class=\"fr\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                @click=\"manualSave\"\n                :disabled=\"!hasUnsavedChanges\"\n              >\n                <i class=\"el-icon-document\"></i> 保存草稿\n              </el-button>\n              <el-dropdown trigger=\"click\" @command=\"handleCacheCommand\">\n                <el-button size=\"mini\" type=\"info\">\n                  <i class=\"el-icon-more\"></i>\n                </el-button>\n                <el-dropdown-menu slot=\"dropdown\">\n                  <el-dropdown-item command=\"clear\">清除缓存</el-dropdown-item>\n                  <el-dropdown-item command=\"export\">导出草稿</el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n              >\n                导入题目\n              </el-button>\n\n              <el-checkbox v-model=\"importOptions.reverse\">\n                按题目顺序倒序导入\n              </el-checkbox>\n\n              <el-checkbox v-model=\"importOptions.allowDuplicate\">\n                允许题目重复\n              </el-checkbox>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 题干和答案在同一行 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                      <span class=\"question-answer-inline\">答案：{{ question.correctAnswer }}</span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button>\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1.建议使用新版office或WPS软件编辑题目文件，仅支持上传.docx/.xlsx格式的文件<br>\n          2.Word导入支持全部题型，Excel导入不支持完形填空题、组合题<br>\n          3.Word导入支持导入图片/公式，Excel导入暂不支持<br>\n          4.题目数量过多、题目文件过大（如图片较多）等情况建议分批导入<br>\n          5.需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 文档导入对话框 -->\n    <batch-import\n      :visible.sync=\"batchImportVisible\"\n      :bank-id=\"bankId\"\n      :default-mode=\"currentImportMode\"\n      @success=\"handleBatchImportSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport BatchImport from './components/BatchImport'\nimport { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'\nimport { batchImportQuestions } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm,\n    BatchImport\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      // 选择状态\n      selectedQuestions: [],\n      // 选择相关\n      selectedQuestions: [],\n      isAllSelected: false,\n      expandedQuestions: [],\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      batchImportVisible: false,\n      currentImportMode: 'excel', // 当前导入模式\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '', // 存储富文本HTML内容用于预览\n      parsedQuestions: [],\n      parseErrors: [],\n      // 全部展开/收起状态\n      allExpanded: true,\n      // 标志位：是否正在从后端设置内容（避免触发前端重新解析）\n      isSettingFromBackend: false,\n      lastSaveTime: '',\n      // 缓存相关\n      cacheKey: 'questionBank_draft_content',\n      autoSaveTimer: null,\n      hasUnsavedChanges: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      // 规范对话框标签页\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时初始化编辑器\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数\n    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n    this.loadCachedContent()\n\n    // 监听页面关闭事件，保存内容\n    window.addEventListener('beforeunload', this.handleBeforeUnload)\n  },\n\n  beforeDestroy() {\n    // 保存当前内容到缓存\n    this.saveToCacheNow()\n\n    // 清理定时器\n    if (this.autoSaveTimer) {\n      clearTimeout(this.autoSaveTimer)\n    }\n\n    // 移除事件监听器\n    window.removeEventListener('beforeunload', this.handleBeforeUnload)\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(error => {\n\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(error => {\n\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n    // 批量导入\n    handleBatchImport() {\n      this.batchImportVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要导出的题目')\n        return\n      }\n      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)\n      // TODO: 实现导出功能\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 这里应该调用批量删除API\n        // 暂时使用单个删除的方式\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.allSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('批量删除失败')\n        })\n      })\n    },\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 批量删除API调用\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.isAllSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('批量删除失败')\n        })\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        this.expandedQuestions.splice(index, 1)\n      } else {\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 批量导入成功回调\n    handleBatchImportSuccess() {\n      this.batchImportVisible = false\n      this.importDrawerVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      done()\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n    // 下载Excel模板\n    downloadExcelTemplate() {\n      this.download('biz/questionBank/downloadExcelTemplate', {}, `题目导入Excel模板.xlsx`)\n    },\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response, file) {\n\n\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n          this.lastSaveTime = new Date().toLocaleString()\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n    // 全部收起\n    collapseAll() {\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', true)\n      })\n    },\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      this.$confirm(`确认导入 ${this.parsedQuestions.length} 道题目吗？`, '确认导入', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n        }\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate\n        }\n\n        const response = await batchImportQuestions(importData)\n\n        if (response.code === 200) {\n          this.$message.success(`成功导入 ${questionsToImport.length} 道题目`)\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 导入成功后清除缓存\n        this.clearCache()\n\n        this.getQuestionList()\n        this.getStatistics()\n      } catch (error) {\n\n        this.$message.error('导入失败')\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              // 错误处理和事件监听\n              on: {\n                pluginsLoaded: function() {\n\n                },\n                instanceReady: function() {\n\n\n                  const editor = evt.editor\n\n                  // 简单的对话框处理 - 参考您提供的代码风格\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n\n\n                      // 简单检查上传完成并切换标签页\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n\n                              // 切换到图像信息标签页\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n\n                        // 10秒后停止检查\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                },\n\n              }\n            })\n          } catch (error) {\n\n\n            // 尝试简化配置\n            try {\n              this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n                height: 'calc(100vh - 200px)',\n                toolbar: [\n                  ['Bold', 'Italic', 'Underline', 'Strike'],\n                  ['NumberedList', 'BulletedList'],\n                  ['Outdent', 'Indent'],\n                  ['Undo', 'Redo'],\n                  ['Link', 'Unlink'],\n                  ['Image', 'RemoveFormat', 'Maximize']\n                ],\n                removeButtons: '',\n                language: 'zh-cn',\n                removePlugins: 'elementspath',\n                resize_enabled: false,\n                extraPlugins: 'image',\n                allowedContent: true,\n                // 图像上传配置 - 参考您提供的标准配置\n                filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n                image_previewText: ' ',\n                // 设置基础路径，让相对路径能正确解析到后端服务器\n                baseHref: 'http://localhost:8802/',\n                // 隐藏不需要的标签页，只保留上传和图像信息\n                removeDialogTabs: 'image:Link;image:advanced',\n                // 添加实例就绪事件处理\n                on: {\n                  instanceReady: function(evt) {\n\n\n                    const editor = evt.editor\n\n                    // 监听对话框显示事件\n                    editor.on('dialogShow', function(evt) {\n                      const dialog = evt.data\n                      if (dialog.getName() === 'image') {\n\n\n                        // 简单检查上传完成并切换标签页\n                        setTimeout(() => {\n                          const checkInterval = setInterval(() => {\n                            try {\n                              const urlField = dialog.getContentElement('info', 'txtUrl')\n                              if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                                clearInterval(checkInterval)\n\n                                // 切换到图像信息标签页\n                                dialog.selectPage('info')\n                              }\n                            } catch (e) {\n                              // 忽略错误\n                            }\n                          }, 500)\n\n                          // 10秒后停止检查\n                          setTimeout(() => clearInterval(checkInterval), 10000)\n                        }, 1000)\n                      }\n                    })\n\n\n                  }\n                }\n              })\n\n            } catch (fallbackError) {\n\n              this.showFallbackEditor = true\n              return\n            }\n          }\n\n          // 监听内容变化\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n              this.lastSaveTime = new Date().toLocaleString()\n\n              // 标记有未保存的更改并保存到缓存\n              this.hasUnsavedChanges = true\n              this.saveToCache()\n            })\n          }\n\n          // 监听按键事件\n          this.richEditor.on('key', () => {\n            setTimeout(() => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n              // 标记有未保存的更改并保存到缓存\n              this.hasUnsavedChanges = true\n              this.saveToCache()\n            }, 100)\n          })\n\n          // 监听实例准备就绪\n          this.richEditor.on('instanceReady', () => {\n            this.editorInitialized = true\n            // 优先加载缓存的HTML内容，如果没有则使用documentContent\n            const contentToLoad = this.documentHtmlContent || this.documentContent\n            if (contentToLoad) {\n              this.richEditor.setData(contentToLoad)\n            }\n          })\n        })\n\n      } catch (error) {\n\n        // 如果CKEditor初始化失败，回退到普通文本框\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = this.documentContent || ''\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化\n        textarea.addEventListener('input', (e) => {\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value // 纯文本模式下HTML内容与文本内容相同\n          this.lastSaveTime = new Date().toLocaleString()\n\n          // 标记有未保存的更改并保存到缓存\n          this.hasUnsavedChanges = true\n          this.saveToCache()\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n    // 去除HTML标签\n    stripHtmlTags(html) {\n      const div = document.createElement('div')\n      div.innerHTML = html\n      return div.textContent || div.innerText || ''\n    },\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        // 如果编辑器还未初始化，保存内容等待初始化完成后设置\n        this.documentContent = content\n        this.documentHtmlContent = content // 同时设置HTML内容\n      }\n    },\n\n\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        // 为每个题目添加collapsed属性\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n\n        // 更新保存时间\n        this.lastSaveTime = new Date().toLocaleString()\n      } catch (error) {\n\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        // 保留图片标签，只移除其他HTML标签\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        // 按行分割内容\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n\n        if (lines.length === 0) {\n\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n                console.error(`❌ 题目 ${questionNumber} 解析失败:`, error)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n            console.error(`❌ 最后题目 ${questionNumber} 解析失败:`, error)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n        console.error('❌ 文档解析失败:', error)\n      }\n\n      console.log('解析完成，共', questions.length, '道题目，', errors.length, '个错误')\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      return /^[A-Za-z][.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 处理img标签中的相对路径\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n\n          // 如果已经是完整路径，不处理\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          // 如果是相对路径，添加后端服务器地址\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          const result = `<img${before}src=\"${fullSrc}\"${after}>`\n          return result\n        })\n\n        return processedContent\n      } catch (error) {\n        console.error('❌ 处理图片路径时出错:', error)\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        console.error('❌ preserveRichTextFormatting 出错:', error)\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 先保存所有图片标签\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        // 移除其他HTML标签，但保留换行\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')  // br标签转换为换行\n          .replace(/<\\/p>/gi, '\\n')       // p结束标签转换为换行\n          .replace(/<p[^>]*>/gi, '\\n')    // p开始标签转换为换行\n          .replace(/<[^>]*>/g, '')        // 移除其他HTML标签\n          .replace(/\\n\\s*\\n/g, '\\n')      // 合并多个换行\n\n        // 恢复图片标签\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        console.error('❌ stripHtmlTagsKeepImages 出错:', error)\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        console.warn('⚠️ 解析选项参数无效')\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n          if (optionMatch) {\n            const optionKey = optionMatch[1].toUpperCase()\n            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n            if (optionKey && optionContent) {\n              options.push({\n                optionKey: optionKey,\n                label: optionKey,\n                optionContent: optionContent,\n                content: optionContent\n              })\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Za-z][.:：．、]\\s*[^\\s]+)*)/g)\n            if (multipleOptionsMatch) {\n              // 处理同一行多个选项的情况\n              const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n              for (const singleOption of singleOptions) {\n                if (!singleOption) continue\n\n                const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                if (match) {\n                  const optionKey = match[1].toUpperCase()\n                  const optionContent = match[2] ? match[2].trim() : ''\n\n                  if (optionKey && optionContent) {\n                    options.push({\n                      optionKey: optionKey,\n                      label: optionKey,\n                      optionContent: optionContent,\n                      content: optionContent\n                    })\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 解析选项时出错:', error)\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          } else {\n            console.warn('⚠️ 不支持的难度级别:', difficulty, '，已忽略')\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 从题干提取答案时出错:', error)\n      }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n        console.error('❌ 解析答案值时出错:', error)\n        return answerText || ''\n      }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题干（移除题号）\n      const firstLine = lines[0]\n      let questionContent = ''\n      let currentLineIndex = 0\n\n      // 如果第一行包含题号，移除题号部分\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．、]\\s*(.*)/)\n      if (numberMatch) {\n        questionContent = numberMatch[2].trim() // 移除题号，只保留题干\n        currentLineIndex = 1\n      } else {\n        // 如果第一行不包含题号，直接作为题干，但仍需清理可能的题号\n        questionContent = this.removeQuestionNumber(firstLine).trim()\n        currentLineIndex = 1\n      }\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度 - 只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n\n    // ==================== 缓存保存相关方法 ====================\n\n    // 加载缓存的内容\n    loadCachedContent() {\n      try {\n        const cachedData = localStorage.getItem(this.cacheKey)\n        if (cachedData) {\n          const data = JSON.parse(cachedData)\n\n          // 恢复内容\n          this.documentContent = data.documentContent || ''\n          this.documentHtmlContent = data.documentHtmlContent || ''\n          this.lastSaveTime = data.lastSaveTime || ''\n\n          // 如果有内容，标记为有未保存的更改\n          if (this.documentContent || this.documentHtmlContent) {\n            this.hasUnsavedChanges = true\n          }\n        }\n      } catch (error) {\n        console.error('❌ 加载缓存内容失败:', error)\n      }\n    },\n\n    // 保存内容到缓存\n    saveToCache() {\n      // 防抖保存，避免频繁写入\n      if (this.autoSaveTimer) {\n        clearTimeout(this.autoSaveTimer)\n      }\n\n      this.autoSaveTimer = setTimeout(() => {\n        this.saveToCacheNow()\n      }, 2000) // 2秒后保存\n    },\n\n    // 立即保存到缓存\n    saveToCacheNow() {\n      try {\n        const dataToSave = {\n          documentContent: this.documentContent || '',\n          documentHtmlContent: this.documentHtmlContent || '',\n          lastSaveTime: this.lastSaveTime || new Date().toLocaleString(),\n          timestamp: Date.now()\n        }\n\n        localStorage.setItem(this.cacheKey, JSON.stringify(dataToSave))\n        this.hasUnsavedChanges = false\n      } catch (error) {\n        console.error('❌ 保存到缓存失败:', error)\n      }\n    },\n\n    // 清除缓存\n    clearCache() {\n      try {\n        localStorage.removeItem(this.cacheKey)\n        this.hasUnsavedChanges = false\n        console.log('🗑️ 缓存已清除')\n      } catch (error) {\n        console.error('❌ 清除缓存失败:', error)\n      }\n    },\n\n    // 页面关闭前的处理\n    handleBeforeUnload(event) {\n      if (this.hasUnsavedChanges) {\n        // 立即保存到缓存\n        this.saveToCacheNow()\n\n        // 提示用户有未保存的更改\n        const message = '您有未保存的更改，确定要离开吗？'\n        event.returnValue = message\n        return message\n      }\n    },\n\n    // 手动保存\n    manualSave() {\n      this.saveToCacheNow()\n      this.$message.success('内容已保存到本地缓存')\n    },\n\n    // 处理缓存相关命令\n    handleCacheCommand(command) {\n      switch (command) {\n        case 'clear':\n          this.$confirm('确定要清除所有缓存的草稿内容吗？', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            this.clearCache()\n            this.documentContent = ''\n            this.documentHtmlContent = ''\n            this.lastSaveTime = ''\n\n            // 清空编辑器内容\n            if (this.richEditor && this.editorInitialized) {\n              this.richEditor.setData('')\n            }\n\n            this.$message.success('缓存已清除')\n          }).catch(() => {\n            // 用户取消\n          })\n          break\n        case 'export':\n          this.exportDraft()\n          break\n      }\n    },\n\n    // 导出草稿\n    exportDraft() {\n      if (!this.documentHtmlContent && !this.documentContent) {\n        this.$message.warning('没有可导出的草稿内容')\n        return\n      }\n\n      const content = this.documentHtmlContent || this.documentContent\n      const blob = new Blob([content], { type: 'text/html;charset=utf-8' })\n      const url = URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = url\n      link.download = `题目草稿_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.html`\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      URL.revokeObjectURL(url)\n\n      this.$message.success('草稿已导出')\n    },\n\n    // ==================== 原有方法 ====================\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        // 如果没有找到匹配的段落，返回原始内容\n        return plainContent\n      } catch (error) {\n        console.error('提取HTML题目内容失败:', error)\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干和答案同一行显示 */\n.question-main-line {\n  display: flex;\n  align-items: flex-start;\n  gap: 15px;\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  flex: 1;\n  margin: 0;\n}\n\n.question-answer-inline {\n  flex-shrink: 0;\n  color: #409eff;\n  font-weight: 500;\n  background: #f0f9ff;\n  padding: 2px 8px;\n  border-radius: 4px;\n  font-size: 13px;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n.question-answer {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.question-explanation {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #909399;\n}\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n\n/* 工具栏样式优化 */\n.toolbar {\n  padding: 10px 15px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.toolbar .orange {\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 8px;\n}\n</style>\n"]}]}