{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_QuestionCard", "_interopRequireDefault", "require", "_QuestionForm", "_BatchImport", "_question", "_questionBank", "_methods", "name", "components", "QuestionCard", "QuestionForm", "BatchImport", "data", "_ref", "bankId", "bankName", "statistics", "total", "singleChoice", "multipleChoice", "judgment", "questionList", "queryParams", "pageNum", "pageSize", "questionType", "difficulty", "questionContent", "expandAll", "selectedQuestions", "_defineProperty2", "default", "reverse", "allowDuplicate", "process", "env", "VUE_APP_BASE_API", "Authorization", "$store", "getters", "token", "watch", "documentContent", "handler", "newVal", "isSettingFromBackend", "trim", "debounceParseDocument", "parsedQuestions", "parseErrors", "immediate", "importDrawerVisible", "_this", "$nextTick", "initRichEditor", "rich<PERSON><PERSON><PERSON>", "destroy", "editorInitialized", "created", "initPage", "debounce", "parseDocument", "uploadData", "uploadHeaders", "mounted", "loadCachedContent", "window", "addEventListener", "handleBeforeUnload", "<PERSON><PERSON><PERSON><PERSON>", "saveToCacheNow", "autoSaveTimer", "clearTimeout", "removeEventListener", "methods", "_this$$route$query", "$route", "query", "$message", "error", "goBack", "getQuestionList", "getStatistics", "$router", "back", "_this2", "params", "convertQueryParams", "listQuestion", "then", "response", "rows", "catch", "convertedParams", "_objectSpread2", "typeMap", "difficultyMap", "Object", "keys", "for<PERSON>ach", "key", "undefined", "_this3", "getQuestionStatistics", "handleBatchImport", "batchImportVisible", "handleAddQuestion", "type", "currentQuestionType", "currentQuestionData", "questionFormVisible", "toggleExpandAll", "expandedQuestions", "handleExportQuestions", "length", "warning", "info", "concat", "handleToggleSelectAll", "isAllSelected", "map", "q", "questionId", "success", "handleBatchDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "deletePromises", "delQuestion", "Promise", "all", "allSelected", "_this5", "handleQuestionSelect", "selected", "includes", "push", "index", "indexOf", "splice", "handleToggleExpand", "handleEditQuestion", "question", "handleCopyQuestion", "copiedQuestion", "createTime", "updateTime", "createBy", "updateBy", "convertQuestionTypeToString", "handleDeleteQuestion", "_this6", "replace", "displayContent", "substring", "handleQuestionFormSuccess", "handleBatchImportSuccess", "handleDrawerClose", "done", "showDocumentImportDialog", "_this7", "isUploading", "isParsing", "uploadComponent", "$refs", "documentUpload", "clearFiles", "documentImportDialogVisible", "showRulesDialog", "activeRuleTab", "rulesDialogVisible", "copyExampleToEditor", "_this8", "htmlTemplate", "setData", "downloadExcelTemplate", "download", "downloadWordTemplate", "beforeUpload", "file", "isValidType", "endsWith", "isLt10M", "size", "handleUploadSuccess", "_this9", "code", "setTimeout", "questions", "collapsed", "allExpanded", "errors", "errorCount", "originalContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentHtmlContent", "lastSaveTime", "Date", "toLocaleString", "msg", "handleUploadError", "collapseAll", "_this0", "$set", "toggleQuestion", "toggleAllQuestions", "_this1", "confirmImport", "_this10", "importQuestions", "_this11", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "questionsToImport", "importData", "_t", "w", "_context", "n", "p", "_toConsumableArray2", "importOptions", "batchImportQuestions", "v", "Error", "clearCache", "a", "_this12", "CKEDITOR", "fallbackToTextarea", "<PERSON><PERSON><PERSON><PERSON>", "document", "getElementById", "innerHTML", "showFallbackEditor", "height", "toolbar", "items", "removeButtons", "language", "removePlugins", "resize_enabled", "extraPlugins", "<PERSON><PERSON><PERSON><PERSON>", "fontSize_sizes", "fontSize_defaultLabel", "colorButton_enableMore", "colorButton_colors", "filebrowserUploadUrl", "image_previewText", "baseHref", "pluginsLoaded", "instanceReady", "editor", "evt", "on", "dialog", "getName", "checkInterval", "setInterval", "urlField", "getContentElement", "getValue", "startsWith", "clearInterval", "selectPage", "e", "removeDialogTabs", "fallback<PERSON><PERSON>r", "rawContent", "getData", "contentWithRelativeUrls", "convertUrlsToRelative", "preserveRichTextFormatting", "stripHtmlTagsKeepImages", "hasUnsavedChanges", "saveToCache", "contentToLoad", "_this13", "textarea", "createElement", "className", "placeholder", "value", "style", "cssText", "target", "append<PERSON><PERSON><PERSON>", "stripHtmlTags", "html", "div", "textContent", "innerText", "content", "func", "wait", "timeout", "executedFunction", "_len", "arguments", "args", "Array", "_key", "later", "apply", "<PERSON><PERSON><PERSON><PERSON>", "location", "origin", "urlRegex", "RegExp", "parseResult", "parseQuestionContent", "message", "lines", "split", "line", "filter", "currentQuestionLines", "questionNumber", "i", "isQuestionStart", "isQuestionStartLine", "isQuestionTypeStart", "questionText", "join", "parsedQuestion", "parseQuestionFromLines", "console", "log", "test", "contentStartIndex", "typeMatch", "match", "typeText", "remainingContent", "isOptionLine", "isAnswerLine", "isExplanationLine", "isDifficultyLine", "cleanLine", "finalQ<PERSON>ionContent", "removeQuestionNumber", "typeName", "getTypeDisplayName", "explanation", "options", "<PERSON><PERSON><PERSON><PERSON>", "optionResult", "parseOptionsFromLines", "parseQuestionMetaFromLines", "processImagePaths", "processedContent", "before", "src", "after", "fullSrc", "result", "_this14", "images", "imageIndex", "contentWithPlaceholders", "finalContent", "img", "startIndex", "isArray", "warn", "optionMatch", "optionKey", "toUpperCase", "optionContent", "label", "multipleOptionsMatch", "singleOptions", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "singleOption", "err", "f", "answerMatch", "parseAnswerValue", "explanationMatch", "difficultyMatch", "extractAnswerFromQuestionContent", "patterns", "_i3", "_patterns", "pattern", "matches", "lastMatch", "answer", "answerText", "trimmedAnswer", "splitByQuestionType", "sections", "typeRegex", "lastIndex", "currentType", "exec", "parseSectionQuestions", "section", "_this15", "convertQuestionType", "questionBlocks", "splitByQuestionNumber", "block", "parseQuestionBlock", "blocks", "numberRegex", "firstLine", "currentLineIndex", "numberMatch", "parseOptions", "nextIndex", "parseQuestionMeta", "currentIndex", "parseAnswer", "extractAnswerFromContent", "bracketPatterns", "_i4", "_bracketPatterns", "matchAll", "getQuestionTypeName", "getQuestionTypeColor", "colorMap", "cachedData", "localStorage", "getItem", "cache<PERSON>ey", "JSON", "parse", "_this16", "dataToSave", "timestamp", "now", "setItem", "stringify", "removeItem", "event", "returnValue", "manualSave", "handleCacheCommand", "command", "_this17", "exportDraft", "blob", "Blob", "url", "URL", "createObjectURL", "link", "href", "toISOString", "slice", "body", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "getFormattedQuestionContent", "htmlContent", "extractQuestionFromHtml", "plainContent", "plainText", "paragraphs", "_iterator2", "_step2", "paragraph", "paragraphText", "cleanParagraphText", "handleSearch", "resetSearch"], "sources": ["src/views/biz/questionBank/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"importDrawerVisible = true\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <span class=\"orange\">最后保存的草稿时间：{{ lastSaveTime || '暂无' }}</span>\n            <span v-if=\"hasUnsavedChanges\" class=\"unsaved-indicator\">●</span>\n            <div class=\"fr\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                @click=\"manualSave\"\n                :disabled=\"!hasUnsavedChanges\"\n              >\n                <i class=\"el-icon-document\"></i> 保存草稿\n              </el-button>\n              <el-dropdown trigger=\"click\" @command=\"handleCacheCommand\">\n                <el-button size=\"mini\" type=\"info\">\n                  <i class=\"el-icon-more\"></i>\n                </el-button>\n                <el-dropdown-menu slot=\"dropdown\">\n                  <el-dropdown-item command=\"clear\">清除缓存</el-dropdown-item>\n                  <el-dropdown-item command=\"export\">导出草稿</el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n              >\n                导入题目\n              </el-button>\n\n              <el-checkbox v-model=\"importOptions.reverse\">\n                按题目顺序倒序导入\n              </el-checkbox>\n\n              <el-checkbox v-model=\"importOptions.allowDuplicate\">\n                允许题目重复\n              </el-checkbox>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 题干和答案在同一行 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                      <span class=\"question-answer-inline\">答案：{{ question.correctAnswer }}</span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button>\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1.建议使用新版office或WPS软件编辑题目文件，仅支持上传.docx/.xlsx格式的文件<br>\n          2.Word导入支持全部题型，Excel导入不支持完形填空题、组合题<br>\n          3.Word导入支持导入图片/公式，Excel导入暂不支持<br>\n          4.题目数量过多、题目文件过大（如图片较多）等情况建议分批导入<br>\n          5.需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 文档导入对话框 -->\n    <batch-import\n      :visible.sync=\"batchImportVisible\"\n      :bank-id=\"bankId\"\n      :default-mode=\"currentImportMode\"\n      @success=\"handleBatchImportSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport BatchImport from './components/BatchImport'\nimport { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'\nimport { batchImportQuestions } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm,\n    BatchImport\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      // 选择状态\n      selectedQuestions: [],\n      // 选择相关\n      selectedQuestions: [],\n      isAllSelected: false,\n      expandedQuestions: [],\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      batchImportVisible: false,\n      currentImportMode: 'excel', // 当前导入模式\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '', // 存储富文本HTML内容用于预览\n      parsedQuestions: [],\n      parseErrors: [],\n      // 全部展开/收起状态\n      allExpanded: true,\n      // 标志位：是否正在从后端设置内容（避免触发前端重新解析）\n      isSettingFromBackend: false,\n      lastSaveTime: '',\n      // 缓存相关\n      cacheKey: 'questionBank_draft_content',\n      autoSaveTimer: null,\n      hasUnsavedChanges: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      // 规范对话框标签页\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时初始化编辑器\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数\n    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n    this.loadCachedContent()\n\n    // 监听页面关闭事件，保存内容\n    window.addEventListener('beforeunload', this.handleBeforeUnload)\n  },\n\n  beforeDestroy() {\n    // 保存当前内容到缓存\n    this.saveToCacheNow()\n\n    // 清理定时器\n    if (this.autoSaveTimer) {\n      clearTimeout(this.autoSaveTimer)\n    }\n\n    // 移除事件监听器\n    window.removeEventListener('beforeunload', this.handleBeforeUnload)\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(error => {\n\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(error => {\n\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n    // 批量导入\n    handleBatchImport() {\n      this.batchImportVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要导出的题目')\n        return\n      }\n      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)\n      // TODO: 实现导出功能\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 这里应该调用批量删除API\n        // 暂时使用单个删除的方式\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.allSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('批量删除失败')\n        })\n      })\n    },\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 批量删除API调用\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.isAllSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('批量删除失败')\n        })\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        this.expandedQuestions.splice(index, 1)\n      } else {\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 批量导入成功回调\n    handleBatchImportSuccess() {\n      this.batchImportVisible = false\n      this.importDrawerVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      done()\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n    // 下载Excel模板\n    downloadExcelTemplate() {\n      this.download('biz/questionBank/downloadExcelTemplate', {}, `题目导入Excel模板.xlsx`)\n    },\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response, file) {\n\n\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n          this.lastSaveTime = new Date().toLocaleString()\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n    // 全部收起\n    collapseAll() {\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', true)\n      })\n    },\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      this.$confirm(`确认导入 ${this.parsedQuestions.length} 道题目吗？`, '确认导入', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n        }\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate\n        }\n\n        const response = await batchImportQuestions(importData)\n\n        if (response.code === 200) {\n          this.$message.success(`成功导入 ${questionsToImport.length} 道题目`)\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 导入成功后清除缓存\n        this.clearCache()\n\n        this.getQuestionList()\n        this.getStatistics()\n      } catch (error) {\n\n        this.$message.error('导入失败')\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              // 错误处理和事件监听\n              on: {\n                pluginsLoaded: function() {\n\n                },\n                instanceReady: function() {\n\n\n                  const editor = evt.editor\n\n                  // 简单的对话框处理 - 参考您提供的代码风格\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n\n\n                      // 简单检查上传完成并切换标签页\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n\n                              // 切换到图像信息标签页\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n\n                        // 10秒后停止检查\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                },\n\n              }\n            })\n          } catch (error) {\n\n\n            // 尝试简化配置\n            try {\n              this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n                height: 'calc(100vh - 200px)',\n                toolbar: [\n                  ['Bold', 'Italic', 'Underline', 'Strike'],\n                  ['NumberedList', 'BulletedList'],\n                  ['Outdent', 'Indent'],\n                  ['Undo', 'Redo'],\n                  ['Link', 'Unlink'],\n                  ['Image', 'RemoveFormat', 'Maximize']\n                ],\n                removeButtons: '',\n                language: 'zh-cn',\n                removePlugins: 'elementspath',\n                resize_enabled: false,\n                extraPlugins: 'image',\n                allowedContent: true,\n                // 图像上传配置 - 参考您提供的标准配置\n                filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n                image_previewText: ' ',\n                // 设置基础路径，让相对路径能正确解析到后端服务器\n                baseHref: 'http://localhost:8802/',\n                // 隐藏不需要的标签页，只保留上传和图像信息\n                removeDialogTabs: 'image:Link;image:advanced',\n                // 添加实例就绪事件处理\n                on: {\n                  instanceReady: function(evt) {\n\n\n                    const editor = evt.editor\n\n                    // 监听对话框显示事件\n                    editor.on('dialogShow', function(evt) {\n                      const dialog = evt.data\n                      if (dialog.getName() === 'image') {\n\n\n                        // 简单检查上传完成并切换标签页\n                        setTimeout(() => {\n                          const checkInterval = setInterval(() => {\n                            try {\n                              const urlField = dialog.getContentElement('info', 'txtUrl')\n                              if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                                clearInterval(checkInterval)\n\n                                // 切换到图像信息标签页\n                                dialog.selectPage('info')\n                              }\n                            } catch (e) {\n                              // 忽略错误\n                            }\n                          }, 500)\n\n                          // 10秒后停止检查\n                          setTimeout(() => clearInterval(checkInterval), 10000)\n                        }, 1000)\n                      }\n                    })\n\n\n                  }\n                }\n              })\n\n            } catch (fallbackError) {\n\n              this.showFallbackEditor = true\n              return\n            }\n          }\n\n          // 监听内容变化\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n              this.lastSaveTime = new Date().toLocaleString()\n\n              // 标记有未保存的更改并保存到缓存\n              this.hasUnsavedChanges = true\n              this.saveToCache()\n            })\n          }\n\n          // 监听按键事件\n          this.richEditor.on('key', () => {\n            setTimeout(() => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n              // 标记有未保存的更改并保存到缓存\n              this.hasUnsavedChanges = true\n              this.saveToCache()\n            }, 100)\n          })\n\n          // 监听实例准备就绪\n          this.richEditor.on('instanceReady', () => {\n            this.editorInitialized = true\n            // 优先加载缓存的HTML内容，如果没有则使用documentContent\n            const contentToLoad = this.documentHtmlContent || this.documentContent\n            if (contentToLoad) {\n              this.richEditor.setData(contentToLoad)\n            }\n          })\n        })\n\n      } catch (error) {\n\n        // 如果CKEditor初始化失败，回退到普通文本框\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = this.documentContent || ''\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化\n        textarea.addEventListener('input', (e) => {\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value // 纯文本模式下HTML内容与文本内容相同\n          this.lastSaveTime = new Date().toLocaleString()\n\n          // 标记有未保存的更改并保存到缓存\n          this.hasUnsavedChanges = true\n          this.saveToCache()\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n    // 去除HTML标签\n    stripHtmlTags(html) {\n      const div = document.createElement('div')\n      div.innerHTML = html\n      return div.textContent || div.innerText || ''\n    },\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        // 如果编辑器还未初始化，保存内容等待初始化完成后设置\n        this.documentContent = content\n        this.documentHtmlContent = content // 同时设置HTML内容\n      }\n    },\n\n\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        // 为每个题目添加collapsed属性\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n\n        // 更新保存时间\n        this.lastSaveTime = new Date().toLocaleString()\n      } catch (error) {\n\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        // 保留图片标签，只移除其他HTML标签\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        // 按行分割内容\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n\n        if (lines.length === 0) {\n\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n                console.error(`❌ 题目 ${questionNumber} 解析失败:`, error)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n            console.error(`❌ 最后题目 ${questionNumber} 解析失败:`, error)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n        console.error('❌ 文档解析失败:', error)\n      }\n\n      console.log('解析完成，共', questions.length, '道题目，', errors.length, '个错误')\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      return /^[A-Za-z][.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 处理img标签中的相对路径\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n\n          // 如果已经是完整路径，不处理\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          // 如果是相对路径，添加后端服务器地址\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          const result = `<img${before}src=\"${fullSrc}\"${after}>`\n          return result\n        })\n\n        return processedContent\n      } catch (error) {\n        console.error('❌ 处理图片路径时出错:', error)\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        console.error('❌ preserveRichTextFormatting 出错:', error)\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 先保存所有图片标签\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        // 移除其他HTML标签，但保留换行\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')  // br标签转换为换行\n          .replace(/<\\/p>/gi, '\\n')       // p结束标签转换为换行\n          .replace(/<p[^>]*>/gi, '\\n')    // p开始标签转换为换行\n          .replace(/<[^>]*>/g, '')        // 移除其他HTML标签\n          .replace(/\\n\\s*\\n/g, '\\n')      // 合并多个换行\n\n        // 恢复图片标签\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        console.error('❌ stripHtmlTagsKeepImages 出错:', error)\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        console.warn('⚠️ 解析选项参数无效')\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n          if (optionMatch) {\n            const optionKey = optionMatch[1].toUpperCase()\n            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n            if (optionKey && optionContent) {\n              options.push({\n                optionKey: optionKey,\n                label: optionKey,\n                optionContent: optionContent,\n                content: optionContent\n              })\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Za-z][.:：．、]\\s*[^\\s]+)*)/g)\n            if (multipleOptionsMatch) {\n              // 处理同一行多个选项的情况\n              const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n              for (const singleOption of singleOptions) {\n                if (!singleOption) continue\n\n                const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                if (match) {\n                  const optionKey = match[1].toUpperCase()\n                  const optionContent = match[2] ? match[2].trim() : ''\n\n                  if (optionKey && optionContent) {\n                    options.push({\n                      optionKey: optionKey,\n                      label: optionKey,\n                      optionContent: optionContent,\n                      content: optionContent\n                    })\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 解析选项时出错:', error)\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          } else {\n            console.warn('⚠️ 不支持的难度级别:', difficulty, '，已忽略')\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 从题干提取答案时出错:', error)\n      }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n        console.error('❌ 解析答案值时出错:', error)\n        return answerText || ''\n      }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题干（移除题号）\n      const firstLine = lines[0]\n      let questionContent = ''\n      let currentLineIndex = 0\n\n      // 如果第一行包含题号，移除题号部分\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．、]\\s*(.*)/)\n      if (numberMatch) {\n        questionContent = numberMatch[2].trim() // 移除题号，只保留题干\n        currentLineIndex = 1\n      } else {\n        // 如果第一行不包含题号，直接作为题干，但仍需清理可能的题号\n        questionContent = this.removeQuestionNumber(firstLine).trim()\n        currentLineIndex = 1\n      }\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度 - 只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n\n    // ==================== 缓存保存相关方法 ====================\n\n    // 加载缓存的内容\n    loadCachedContent() {\n      try {\n        const cachedData = localStorage.getItem(this.cacheKey)\n        if (cachedData) {\n          const data = JSON.parse(cachedData)\n\n          // 恢复内容\n          this.documentContent = data.documentContent || ''\n          this.documentHtmlContent = data.documentHtmlContent || ''\n          this.lastSaveTime = data.lastSaveTime || ''\n\n          // 如果有内容，标记为有未保存的更改\n          if (this.documentContent || this.documentHtmlContent) {\n            this.hasUnsavedChanges = true\n          }\n        }\n      } catch (error) {\n        console.error('❌ 加载缓存内容失败:', error)\n      }\n    },\n\n    // 保存内容到缓存\n    saveToCache() {\n      // 防抖保存，避免频繁写入\n      if (this.autoSaveTimer) {\n        clearTimeout(this.autoSaveTimer)\n      }\n\n      this.autoSaveTimer = setTimeout(() => {\n        this.saveToCacheNow()\n      }, 2000) // 2秒后保存\n    },\n\n    // 立即保存到缓存\n    saveToCacheNow() {\n      try {\n        const dataToSave = {\n          documentContent: this.documentContent || '',\n          documentHtmlContent: this.documentHtmlContent || '',\n          lastSaveTime: this.lastSaveTime || new Date().toLocaleString(),\n          timestamp: Date.now()\n        }\n\n        localStorage.setItem(this.cacheKey, JSON.stringify(dataToSave))\n        this.hasUnsavedChanges = false\n      } catch (error) {\n        console.error('❌ 保存到缓存失败:', error)\n      }\n    },\n\n    // 清除缓存\n    clearCache() {\n      try {\n        localStorage.removeItem(this.cacheKey)\n        this.hasUnsavedChanges = false\n        console.log('🗑️ 缓存已清除')\n      } catch (error) {\n        console.error('❌ 清除缓存失败:', error)\n      }\n    },\n\n    // 页面关闭前的处理\n    handleBeforeUnload(event) {\n      if (this.hasUnsavedChanges) {\n        // 立即保存到缓存\n        this.saveToCacheNow()\n\n        // 提示用户有未保存的更改\n        const message = '您有未保存的更改，确定要离开吗？'\n        event.returnValue = message\n        return message\n      }\n    },\n\n    // 手动保存\n    manualSave() {\n      this.saveToCacheNow()\n      this.$message.success('内容已保存到本地缓存')\n    },\n\n    // 处理缓存相关命令\n    handleCacheCommand(command) {\n      switch (command) {\n        case 'clear':\n          this.$confirm('确定要清除所有缓存的草稿内容吗？', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            this.clearCache()\n            this.documentContent = ''\n            this.documentHtmlContent = ''\n            this.lastSaveTime = ''\n\n            // 清空编辑器内容\n            if (this.richEditor && this.editorInitialized) {\n              this.richEditor.setData('')\n            }\n\n            this.$message.success('缓存已清除')\n          }).catch(() => {\n            // 用户取消\n          })\n          break\n        case 'export':\n          this.exportDraft()\n          break\n      }\n    },\n\n    // 导出草稿\n    exportDraft() {\n      if (!this.documentHtmlContent && !this.documentContent) {\n        this.$message.warning('没有可导出的草稿内容')\n        return\n      }\n\n      const content = this.documentHtmlContent || this.documentContent\n      const blob = new Blob([content], { type: 'text/html;charset=utf-8' })\n      const url = URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = url\n      link.download = `题目草稿_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.html`\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      URL.revokeObjectURL(url)\n\n      this.$message.success('草稿已导出')\n    },\n\n    // ==================== 原有方法 ====================\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        // 如果没有找到匹配的段落，返回原始内容\n        return plainContent\n      } catch (error) {\n        console.error('提取HTML题目内容失败:', error)\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干和答案同一行显示 */\n.question-main-line {\n  display: flex;\n  align-items: flex-start;\n  gap: 15px;\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  flex: 1;\n  margin: 0;\n}\n\n.question-answer-inline {\n  flex-shrink: 0;\n  color: #409eff;\n  font-weight: 500;\n  background: #f0f9ff;\n  padding: 2px 8px;\n  border-radius: 4px;\n  font-size: 13px;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n.question-answer {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.question-explanation {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #909399;\n}\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n\n/* 工具栏样式优化 */\n.toolbar {\n  padding: 10px 15px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.toolbar .orange {\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 8px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuhBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AAAA,IAAAK,QAAA,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,IAAA;IACA,OAAAA,IAAA;MACA;MACAC,MAAA;MACAC,QAAA;MACA;MACAC,UAAA;QACAC,KAAA;QACAC,YAAA;QACAC,cAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAJ,KAAA;MACAK,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,iBAAA;IAAA,OAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,uBAEA,sBACA,6BACA,4BAEA,+BACA,kCACA,8BAEA,8BACA,6BACA,6BAEA,SAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,yBACA,wBACA,oBACA,oBAEA,+BAEA,wBACA,iBAEA,gDACA,4BACA,uCACA,YAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,wBACA,yBAEA,4BAEA,qBACA,yBACA;MACAmB,OAAA;MACAC,cAAA;IACA,iBAEAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,yDACA;MACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA,kBACA,mBAEA,4BACA;EAEA;EAEAC,KAAA;IACA;IACAC,eAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,SAAAC,oBAAA;UACA;QACA;QAIA,IAAAD,MAAA,IAAAA,MAAA,CAAAE,IAAA;UACA,KAAAC,qBAAA;QACA;UACA,KAAAC,eAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAC,SAAA;IACA;IACA;IACAC,mBAAA;MACAR,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAQ,KAAA;QACA,IAAAR,MAAA;UACA;UACA,KAAAS,SAAA;YACAD,KAAA,CAAAE,cAAA;UACA;QACA;UACA;UACA,SAAAC,UAAA;YACA,KAAAA,UAAA,CAAAC,OAAA;YACA,KAAAD,UAAA;YACA,KAAAE,iBAAA;UACA;QACA;MACA;MACAP,SAAA;IACA;EACA;EAEAQ,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA;IACA,KAAAZ,qBAAA,QAAAa,QAAA,MAAAC,aAAA;IACA;IACA,KAAAC,UAAA;MACAhD,MAAA,OAAAA;IACA;IACA,KAAAiD,aAAA;MACA1B,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA;EACA;EAEAwB,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,iBAAA;;IAEA;IACAC,MAAA,CAAAC,gBAAA,sBAAAC,kBAAA;EACA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACA,KAAAC,cAAA;;IAEA;IACA,SAAAC,aAAA;MACAC,YAAA,MAAAD,aAAA;IACA;;IAEA;IACAL,MAAA,CAAAO,mBAAA,sBAAAL,kBAAA;;IAEA;IACA,SAAAb,UAAA;MACA,KAAAA,UAAA,CAAAC,OAAA;MACA,KAAAD,UAAA;IACA;EACA;EACAmB,OAAA,GAAApE,QAAA;IACA;IACAqD,QAAA,WAAAA,SAAA;MACA,IAAAgB,kBAAA,QAAAC,MAAA,CAAAC,KAAA;QAAA/D,MAAA,GAAA6D,kBAAA,CAAA7D,MAAA;QAAAC,QAAA,GAAA4D,kBAAA,CAAA5D,QAAA;MACA,KAAAD,MAAA;QACA,KAAAgE,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MACA,KAAAlE,MAAA,GAAAA,MAAA;MACA,KAAAC,QAAA,GAAAA,QAAA;MACA,KAAAO,WAAA,CAAAR,MAAA,GAAAA,MAAA;MACA,KAAAmE,eAAA;MACA,KAAAC,aAAA;IACA;IACA;IACAF,MAAA,WAAAA,OAAA;MACA,KAAAG,OAAA,CAAAC,IAAA;IACA;IACA;IACAH,eAAA,WAAAA,gBAAA;MAAA,IAAAI,MAAA;MACA;MACA,IAAAC,MAAA,QAAAC,kBAAA,MAAAjE,WAAA;MACA,IAAAkE,sBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,MAAA,CAAAhE,YAAA,GAAAqE,QAAA,CAAAC,IAAA;QACAN,MAAA,CAAApE,KAAA,GAAAyE,QAAA,CAAAzE,KAAA;MACA,GAAA2E,KAAA,WAAAb,KAAA;QAEAM,MAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAAD,MAAA;MACA,IAAAO,eAAA,OAAAC,cAAA,CAAA/D,OAAA,MAAAuD,MAAA;;MAEA;MACA,IAAAO,eAAA,CAAApE,YAAA;QACA,IAAAsE,OAAA;UACA;UACA;UACA;QACA;QACAF,eAAA,CAAApE,YAAA,GAAAsE,OAAA,CAAAF,eAAA,CAAApE,YAAA,KAAAoE,eAAA,CAAApE,YAAA;MACA;;MAEA;MACA,IAAAoE,eAAA,CAAAnE,UAAA;QACA,IAAAsE,aAAA;UACA;UACA;UACA;QACA;QACAH,eAAA,CAAAnE,UAAA,GAAAsE,aAAA,CAAAH,eAAA,CAAAnE,UAAA,KAAAmE,eAAA,CAAAnE,UAAA;MACA;;MAEA;MACAuE,MAAA,CAAAC,IAAA,CAAAL,eAAA,EAAAM,OAAA,WAAAC,GAAA;QACA,IAAAP,eAAA,CAAAO,GAAA,YAAAP,eAAA,CAAAO,GAAA,cAAAP,eAAA,CAAAO,GAAA,MAAAC,SAAA;UACA,OAAAR,eAAA,CAAAO,GAAA;QACA;MACA;MAEA,OAAAP,eAAA;IACA;IACA;IACAX,aAAA,WAAAA,cAAA;MAAA,IAAAoB,MAAA;MACA,IAAAC,+BAAA,OAAAzF,MAAA,EAAA2E,IAAA,WAAAC,QAAA;QACAY,MAAA,CAAAtF,UAAA,GAAA0E,QAAA,CAAA9E,IAAA;MACA,GAAAgF,KAAA,WAAAb,KAAA;QAEA;QACAuB,MAAA,CAAAtF,UAAA;UACAC,KAAA;UACAC,YAAA;UACAC,cAAA;UACAC,QAAA;QACA;MACA;IACA;IACA;IACAoF,iBAAA,WAAAA,kBAAA;MACA,KAAAC,kBAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAC,mBAAA,GAAAD,IAAA;MACA,KAAAE,mBAAA;MACA,KAAAC,mBAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAnF,SAAA,SAAAA,SAAA;MACA,UAAAA,SAAA;QACA,KAAAoF,iBAAA;MACA;IACA;IAIA;IACAC,qBAAA,WAAAA,sBAAA;MACA,SAAApF,iBAAA,CAAAqF,MAAA;QACA,KAAApC,QAAA,CAAAqC,OAAA;QACA;MACA;MACA,KAAArC,QAAA,CAAAsC,IAAA,6BAAAC,MAAA,MAAAxF,iBAAA,CAAAqF,MAAA;MACA;IACA;IAEA;IACAI,qBAAA,WAAAA,sBAAA;MACA,KAAAC,aAAA,SAAAA,aAAA;MACA,SAAAA,aAAA;QACA;QACA,KAAA1F,iBAAA,QAAAR,YAAA,CAAAmG,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,UAAA;QAAA;QACA,KAAA5C,QAAA,CAAA6C,OAAA,uBAAAN,MAAA,MAAAxF,iBAAA,CAAAqF,MAAA;MACA;QACA;QACA,KAAArF,iBAAA;QACA,KAAAiD,QAAA,CAAA6C,OAAA;MACA;IACA;IAIA;IACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAAhG,iBAAA,CAAAqF,MAAA;QACA,KAAApC,QAAA,CAAAqC,OAAA;QACA;MACA;MAEA,KAAAW,QAAA,+CAAAT,MAAA,MAAAxF,iBAAA,CAAAqF,MAAA;QACAa,iBAAA;QACAC,gBAAA;QACArB,IAAA;MACA,GAAAlB,IAAA;QACA;QACA;QACA,IAAAwC,cAAA,GAAAJ,MAAA,CAAAhG,iBAAA,CAAA2F,GAAA,WAAAE,UAAA;UAAA,OACA,IAAAQ,qBAAA,EAAAR,UAAA;QAAA,CACA;QAEAS,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAAxC,IAAA;UACAoC,MAAA,CAAA/C,QAAA,CAAA6C,OAAA,6BAAAN,MAAA,CAAAQ,MAAA,CAAAhG,iBAAA,CAAAqF,MAAA;UACAW,MAAA,CAAAhG,iBAAA;UACAgG,MAAA,CAAAQ,WAAA;UACAR,MAAA,CAAA5C,eAAA;UACA4C,MAAA,CAAA3C,aAAA;QACA,GAAAU,KAAA,WAAAb,KAAA;UAEA8C,MAAA,CAAA/C,QAAA,CAAAC,KAAA;QACA;MACA;IACA;EAAA,OAAAjD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,gCAAAsH,kBAAA,EAGA;IAAA,IAAAU,MAAA;IACA,SAAAzG,iBAAA,CAAAqF,MAAA;MACA,KAAApC,QAAA,CAAAqC,OAAA;MACA;IACA;IAEA,KAAAW,QAAA,+CAAAT,MAAA,MAAAxF,iBAAA,CAAAqF,MAAA;MACAa,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAlB,IAAA;MACA;MACA,IAAAwC,cAAA,GAAAK,MAAA,CAAAzG,iBAAA,CAAA2F,GAAA,WAAAE,UAAA;QAAA,OACA,IAAAQ,qBAAA,EAAAR,UAAA;MAAA,CACA;MAEAS,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAAxC,IAAA;QACA6C,MAAA,CAAAxD,QAAA,CAAA6C,OAAA,6BAAAN,MAAA,CAAAiB,MAAA,CAAAzG,iBAAA,CAAAqF,MAAA;QACAoB,MAAA,CAAAzG,iBAAA;QACAyG,MAAA,CAAAf,aAAA;QACAe,MAAA,CAAArD,eAAA;QACAqD,MAAA,CAAApD,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QAEAuD,MAAA,CAAAxD,QAAA,CAAAC,KAAA;MACA;IACA,GAAAa,KAAA;MACA0C,MAAA,CAAAxD,QAAA,CAAAsC,IAAA;IACA;EACA,qCAGAmB,qBAAAb,UAAA,EAAAc,QAAA;IACA,IAAAA,QAAA;MACA,UAAA3G,iBAAA,CAAA4G,QAAA,CAAAf,UAAA;QACA,KAAA7F,iBAAA,CAAA6G,IAAA,CAAAhB,UAAA;MACA;IACA;MACA,IAAAiB,KAAA,QAAA9G,iBAAA,CAAA+G,OAAA,CAAAlB,UAAA;MACA,IAAAiB,KAAA;QACA,KAAA9G,iBAAA,CAAAgH,MAAA,CAAAF,KAAA;MACA;IACA;;IAEA;IACA,KAAApB,aAAA,QAAA1F,iBAAA,CAAAqF,MAAA,UAAA7F,YAAA,CAAA6F,MAAA;EACA,mCAEA4B,mBAAApB,UAAA;IACA,IAAAiB,KAAA,QAAA3B,iBAAA,CAAA4B,OAAA,CAAAlB,UAAA;IACA,IAAAiB,KAAA;MACA,KAAA3B,iBAAA,CAAA6B,MAAA,CAAAF,KAAA;IACA;MACA,KAAA3B,iBAAA,CAAA0B,IAAA,CAAAhB,UAAA;IACA;EACA,mCAEAqB,mBAAAC,QAAA;IACA,KAAAnC,mBAAA,GAAAmC,QAAA;IACA,KAAApC,mBAAA,GAAAoC,QAAA,CAAAvH,YAAA;IACA,KAAAqF,mBAAA;EACA,mCAEAmC,mBAAAD,QAAA;IACA;IACA,IAAAE,cAAA,OAAApD,cAAA,CAAA/D,OAAA,MAAA+D,cAAA,CAAA/D,OAAA,MACAiH,QAAA;MACAtB,UAAA;MAAA;MACAyB,UAAA;MACAC,UAAA;MACAC,QAAA;MACAC,QAAA;IAAA,EACA;;IAEA;IACA,KAAAzC,mBAAA,GAAAqC,cAAA;IACA,KAAAtC,mBAAA,QAAA2C,2BAAA,CAAAP,QAAA,CAAAvH,YAAA;IACA,KAAAqF,mBAAA;EACA,4CAGAyC,4BAAA5C,IAAA;IACA,IAAAZ,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAY,IAAA,KAAAA,IAAA;EACA,qCAEA6C,qBAAAR,QAAA;IAAA,IAAAS,MAAA;IACA,IAAA9H,eAAA,GAAAqH,QAAA,CAAArH,eAAA,CAAA+H,OAAA;IACA,IAAAC,cAAA,GAAAhI,eAAA,CAAAuF,MAAA,QAAAvF,eAAA,CAAAiI,SAAA,kBAAAjI,eAAA;IACA,KAAAmG,QAAA,0CAAAT,MAAA,CAAAsC,cAAA;MACA5B,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAlB,IAAA;MACA,IAAAyC,qBAAA,EAAAc,QAAA,CAAAtB,UAAA,EAAAjC,IAAA;QACAgE,MAAA,CAAA3E,QAAA,CAAA6C,OAAA;QACA8B,MAAA,CAAAxE,eAAA;QACAwE,MAAA,CAAAvE,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QAEA0E,MAAA,CAAA3E,QAAA,CAAAC,KAAA;MACA;IACA;EACA,0CAEA8E,0BAAA;IACA,KAAA/C,mBAAA;IACA,KAAA7B,eAAA;IACA,KAAAC,aAAA;EACA,yCAEA4E,yBAAA;IACA,KAAArD,kBAAA;IACA,KAAAtD,mBAAA;IACA,KAAA8B,eAAA;IACA,KAAAC,aAAA;EACA,kCAKA6E,kBAAAC,IAAA;IACAA,IAAA;EACA,QAAAlI,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,uCAGA2J,yBAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAC,WAAA;IACA,KAAAC,SAAA;;IAEA;IACA,KAAA/G,SAAA;MACA,IAAAgH,eAAA,GAAAH,MAAA,CAAAI,KAAA,CAAAC,cAAA;MACA,IAAAF,eAAA;QACAA,eAAA,CAAAG,UAAA;MACA;IACA;IAEA,KAAAC,2BAAA;EAEA,gCAGAC,gBAAA;IACA,KAAAC,aAAA;IACA,KAAAC,kBAAA;EACA,oCAGAC,oBAAA;IAAA,IAAAC,MAAA;IACA;IACA,IAAAC,YAAA,muDAuBAjI,IAAA;;IAEA;IACA,SAAAS,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAAyH,OAAA,CAAAD,YAAA;IAEA;MACA;MACA,KAAA1H,SAAA;QACA,IAAAyH,MAAA,CAAAvH,UAAA,IAAAuH,MAAA,CAAArH,iBAAA;UACAqH,MAAA,CAAAvH,UAAA,CAAAyH,OAAA,CAAAD,YAAA;QAEA;MACA;IACA;;IAEA;IACA,KAAAH,kBAAA;;IAEA;IACA,KAAA9F,QAAA,CAAA6C,OAAA;EAGA,sCAGAsD,sBAAA;IACA,KAAAC,QAAA;EACA,qCAGAC,qBAAA;IACA,KAAAD,QAAA;EACA,6BAGAE,aAAAC,IAAA;IAGA,IAAAC,WAAA,GAAAD,IAAA,CAAA1E,IAAA,kFACA0E,IAAA,CAAA1E,IAAA,4EACA0E,IAAA,CAAA9K,IAAA,CAAAgL,QAAA,aAAAF,IAAA,CAAA9K,IAAA,CAAAgL,QAAA;IACA,IAAAC,OAAA,GAAAH,IAAA,CAAAI,IAAA;IAEA,KAAAH,WAAA;MACA,KAAAxG,QAAA,CAAAC,KAAA;MACA;IACA;IACA,KAAAyG,OAAA;MACA,KAAA1G,QAAA,CAAAC,KAAA;MACA;IACA;;IAEA;IACA,KAAAjB,UAAA,CAAAhD,MAAA,QAAAA,MAAA;;IAEA;IACA,KAAAqJ,WAAA;IACA,KAAAC,SAAA;IAIA;EACA,oCAGAsB,oBAAAhG,QAAA,EAAA2F,IAAA;IAAA,IAAAM,MAAA;IAGA,IAAAjG,QAAA,CAAAkG,IAAA;MACA;MACA,KAAAzB,WAAA;MACA,KAAAC,SAAA;;MAIA;MACA,KAAApH,eAAA;MACA,KAAAC,WAAA;;MAEA;MACA4I,UAAA;QACAF,MAAA,CAAAlB,2BAAA;QACAkB,MAAA,CAAAvB,SAAA;MACA;;MAEA;MACA,KAAAvH,oBAAA;;MAEA;MACA,IAAA6C,QAAA,CAAAoG,SAAA,IAAApG,QAAA,CAAAoG,SAAA,CAAA5E,MAAA;QACA,KAAAlE,eAAA,GAAA0C,QAAA,CAAAoG,SAAA,CAAAtE,GAAA,WAAAwB,QAAA;UAAA,WAAAlD,cAAA,CAAA/D,OAAA,MAAA+D,cAAA,CAAA/D,OAAA,MACAiH,QAAA;YACA+C,SAAA;UAAA;QAAA,CACA;QACA;QACA,KAAAC,WAAA;QACA,KAAA/I,WAAA,GAAAyC,QAAA,CAAAuG,MAAA;;QAEA;QACA,IAAAC,UAAA,GAAAxG,QAAA,CAAAuG,MAAA,GAAAvG,QAAA,CAAAuG,MAAA,CAAA/E,MAAA;QACA,IAAAgF,UAAA;UACA,KAAApH,QAAA,CAAA6C,OAAA,mCAAAN,MAAA,CAAA3B,QAAA,CAAAoG,SAAA,CAAA5E,MAAA,sCAAAG,MAAA,CAAA6E,UAAA;QACA;UACA,KAAApH,QAAA,CAAA6C,OAAA,mCAAAN,MAAA,CAAA3B,QAAA,CAAAoG,SAAA,CAAA5E,MAAA;QACA;MAGA;QACA,KAAApC,QAAA,CAAAC,KAAA;QACA,KAAA/B,eAAA;QACA,KAAAC,WAAA,GAAAyC,QAAA,CAAAuG,MAAA;MAGA;;MAEA;MACA,IAAAvG,QAAA,CAAAyG,eAAA;QACA,KAAAC,gBAAA,CAAA1G,QAAA,CAAAyG,eAAA;QACA,KAAAzJ,eAAA,GAAAgD,QAAA,CAAAyG,eAAA;QACA,KAAAE,mBAAA,GAAA3G,QAAA,CAAAyG,eAAA;QACA,KAAAG,YAAA,OAAAC,IAAA,GAAAC,cAAA;MACA;;MAEA;MACAX,UAAA;QACAF,MAAA,CAAA9I,oBAAA;MACA;IACA;MAEA,KAAAiC,QAAA,CAAAC,KAAA,CAAAW,QAAA,CAAA+G,GAAA;MACA;MACA,KAAAtC,WAAA;MACA,KAAAC,SAAA;IACA;EACA,kCAGAsC,kBAAA3H,KAAA,EAAAsG,IAAA;IAEA,KAAAvG,QAAA,CAAAC,KAAA;;IAEA;IACA,KAAAoF,WAAA;IACA,KAAAC,SAAA;EACA,4BAGAuC,YAAA;IAAA,IAAAC,MAAA;IACA,KAAA5J,eAAA,CAAAmD,OAAA,WAAA6C,QAAA;MACA4D,MAAA,CAAAC,IAAA,CAAA7D,QAAA;IACA;EACA,+BAGA8D,eAAAnE,KAAA;IACA,IAAAK,QAAA,QAAAhG,eAAA,CAAA2F,KAAA;IACA,KAAAkE,IAAA,CAAA7D,QAAA,gBAAAA,QAAA,CAAA+C,SAAA;EACA,QAAAjK,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,iCAGAyM,mBAAA;IAAA,IAAAC,MAAA;IACA,KAAAhB,WAAA,SAAAA,WAAA;IACA,KAAAhJ,eAAA,CAAAmD,OAAA,WAAA6C,QAAA;MACAgE,MAAA,CAAAH,IAAA,CAAA7D,QAAA,gBAAAgE,MAAA,CAAAhB,WAAA;IACA;EAEA,8BAGAiB,cAAA;IAAA,IAAAC,OAAA;IACA,SAAAlK,eAAA,CAAAkE,MAAA;MACA,KAAApC,QAAA,CAAAqC,OAAA;MACA;IACA;IAEA,KAAAW,QAAA,6BAAAT,MAAA,MAAArE,eAAA,CAAAkE,MAAA;MACAa,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAlB,IAAA;MACAyH,OAAA,CAAAC,eAAA;IACA,GAAAvH,KAAA;EACA,gCAGAuH,gBAAA;IAAA,IAAAC,OAAA;IAAA,WAAAC,kBAAA,CAAAtL,OAAA,mBAAAuL,aAAA,CAAAvL,OAAA,IAAAwL,CAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,UAAA,EAAAhI,QAAA,EAAAiI,EAAA;MAAA,WAAAL,aAAA,CAAAvL,OAAA,IAAA6L,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAEA;YACAN,iBAAA,OAAAO,mBAAA,CAAAjM,OAAA,EAAAqL,OAAA,CAAApK,eAAA;YAEA,IAAAoK,OAAA,CAAAa,aAAA,CAAAjM,OAAA;cACAyL,iBAAA,CAAAzL,OAAA;YACA;;YAEA;YACA0L,UAAA;cACA5M,MAAA,EAAAsM,OAAA,CAAAtM,MAAA;cACAgL,SAAA,EAAA2B,iBAAA;cACAxL,cAAA,EAAAmL,OAAA,CAAAa,aAAA,CAAAhM;YACA;YAAA4L,QAAA,CAAAC,CAAA;YAAA,OAEA,IAAAI,kCAAA,EAAAR,UAAA;UAAA;YAAAhI,QAAA,GAAAmI,QAAA,CAAAM,CAAA;YAAA,MAEAzI,QAAA,CAAAkG,IAAA;cAAAiC,QAAA,CAAAC,CAAA;cAAA;YAAA;YACAV,OAAA,CAAAtI,QAAA,CAAA6C,OAAA,6BAAAN,MAAA,CAAAoG,iBAAA,CAAAvG,MAAA;YAAA2G,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAA,MAEA,IAAAM,KAAA,CAAA1I,QAAA,CAAA+G,GAAA;UAAA;YAEAW,OAAA,CAAAjK,mBAAA;YACAiK,OAAA,CAAA1K,eAAA;YACA0K,OAAA,CAAAf,mBAAA;YACAe,OAAA,CAAApK,eAAA;YACAoK,OAAA,CAAAnK,WAAA;;YAEA;YACAmK,OAAA,CAAAiB,UAAA;YAEAjB,OAAA,CAAAnI,eAAA;YACAmI,OAAA,CAAAlI,aAAA;YAAA2I,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAAAJ,EAAA,GAAAE,QAAA,CAAAM,CAAA;YAGAf,OAAA,CAAAtI,QAAA,CAAAC,KAAA;UAAA;YAAA,OAAA8I,QAAA,CAAAS,CAAA;QAAA;MAAA,GAAAd,OAAA;IAAA;EAEA,+BAGAlK,eAAA;IAAA,IAAAiL,OAAA;IACA,SAAA9K,iBAAA;MACA;IACA;;IAEA;IACA,KAAAS,MAAA,CAAAsK,QAAA;MAEA,KAAAC,kBAAA;MACA;IACA;IAEA;MACA;MACA,SAAAlL,UAAA;QACA,KAAAA,UAAA,CAAAC,OAAA;QACA,KAAAD,UAAA;MACA;;MAEA;MACA,IAAAmL,eAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,eAAA;QAEA;MACA;;MAEA;MACAA,eAAA,CAAAG,SAAA;;MAEA;MACA,KAAAxL,SAAA;QACA;QACA,KAAAa,MAAA,CAAAsK,QAAA,KAAAtK,MAAA,CAAAsK,QAAA,CAAA9E,OAAA;UAEA6E,OAAA,CAAAO,kBAAA;UACA;QACA;QAEA;UACA;UACAP,OAAA,CAAAhL,UAAA,GAAAW,MAAA,CAAAsK,QAAA,CAAA9E,OAAA,6BAAA5H,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;YACAgN,MAAA;YAAA;YACAC,OAAA,GACA;cAAAzO,IAAA;cAAA0O,KAAA;YAAA,GACA;cAAA1O,IAAA;cAAA0O,KAAA;YAAA,GACA;cAAA1O,IAAA;cAAA0O,KAAA;YAAA,GACA;cAAA1O,IAAA;cAAA0O,KAAA;YAAA,GACA;cAAA1O,IAAA;cAAA0O,KAAA;YAAA,GACA;cAAA1O,IAAA;cAAA0O,KAAA;YAAA,GACA;cAAA1O,IAAA;cAAA0O,KAAA;YAAA,GACA;cAAA1O,IAAA;cAAA0O,KAAA;YAAA,GACA;cAAA1O,IAAA;cAAA0O,KAAA;YAAA,EACA;YACAC,aAAA;YACAC,QAAA;YACAC,aAAA;YACAC,cAAA;YACAC,YAAA;YACAC,cAAA;YACA;YACAC,cAAA;YACAC,qBAAA;YACA;YACAC,sBAAA;YACAC,kBAAA;YACA;YACAC,oBAAA,EAAA1N,OAAA,CAAAC,GAAA,CAAAC,gBAAA;YACAyN,iBAAA;YACA;YACAC,QAAA;UAAA,wBAEA,uCACA,2BAEA,oCAEA;YACAC,aAAA,WAAAA,cAAA,GAEA;YACAC,aAAA,WAAAA,cAAA;cAGA,IAAAC,MAAA,GAAAC,GAAA,CAAAD,MAAA;;cAEA;cACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;gBACA,IAAAE,MAAA,GAAAF,GAAA,CAAAtP,IAAA;gBACA,IAAAwP,MAAA,CAAAC,OAAA;kBAGA;kBACAxE,UAAA;oBACA,IAAAyE,aAAA,GAAAC,WAAA;sBACA;wBACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;wBACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;0BACAC,aAAA,CAAAN,aAAA;;0BAEA;0BACAF,MAAA,CAAAS,UAAA;wBACA;sBACA,SAAAC,CAAA;wBACA;sBAAA;oBAEA;;oBAEA;oBACAjF,UAAA;sBAAA,OAAA+E,aAAA,CAAAN,aAAA;oBAAA;kBACA;gBACA;cACA;YACA;UAEA,EACA;QACA,SAAAvL,KAAA;UAGA;UACA;YACAwJ,OAAA,CAAAhL,UAAA,GAAAW,MAAA,CAAAsK,QAAA,CAAA9E,OAAA;cACAqF,MAAA;cACAC,OAAA,GACA,2CACA,kCACA,uBACA,kBACA,oBACA,sCACA;cACAE,aAAA;cACAC,QAAA;cACAC,aAAA;cACAC,cAAA;cACAC,YAAA;cACAC,cAAA;cACA;cACAK,oBAAA,EAAA1N,OAAA,CAAAC,GAAA,CAAAC,gBAAA;cACAyN,iBAAA;cACA;cACAC,QAAA;cACA;cACAiB,gBAAA;cACA;cACAZ,EAAA;gBACAH,aAAA,WAAAA,cAAAE,GAAA;kBAGA,IAAAD,MAAA,GAAAC,GAAA,CAAAD,MAAA;;kBAEA;kBACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;oBACA,IAAAE,MAAA,GAAAF,GAAA,CAAAtP,IAAA;oBACA,IAAAwP,MAAA,CAAAC,OAAA;sBAGA;sBACAxE,UAAA;wBACA,IAAAyE,aAAA,GAAAC,WAAA;0BACA;4BACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;4BACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;8BACAC,aAAA,CAAAN,aAAA;;8BAEA;8BACAF,MAAA,CAAAS,UAAA;4BACA;0BACA,SAAAC,CAAA;4BACA;0BAAA;wBAEA;;wBAEA;wBACAjF,UAAA;0BAAA,OAAA+E,aAAA,CAAAN,aAAA;wBAAA;sBACA;oBACA;kBACA;gBAGA;cACA;YACA;UAEA,SAAAU,aAAA;YAEAzC,OAAA,CAAAO,kBAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAP,OAAA,CAAAhL,UAAA,IAAAgL,OAAA,CAAAhL,UAAA,CAAA4M,EAAA;UACA5B,OAAA,CAAAhL,UAAA,CAAA4M,EAAA;YACA,IAAAc,UAAA,GAAA1C,OAAA,CAAAhL,UAAA,CAAA2N,OAAA;YACA,IAAAC,uBAAA,GAAA5C,OAAA,CAAA6C,qBAAA,CAAAH,UAAA;;YAEA;YACA1C,OAAA,CAAAlC,mBAAA,GAAAkC,OAAA,CAAA8C,0BAAA,CAAAF,uBAAA;YACA;YACA5C,OAAA,CAAA7L,eAAA,GAAA6L,OAAA,CAAA+C,uBAAA,CAAAH,uBAAA;YAEA5C,OAAA,CAAAjC,YAAA,OAAAC,IAAA,GAAAC,cAAA;;YAEA;YACA+B,OAAA,CAAAgD,iBAAA;YACAhD,OAAA,CAAAiD,WAAA;UACA;QACA;;QAEA;QACAjD,OAAA,CAAAhL,UAAA,CAAA4M,EAAA;UACAtE,UAAA;YACA,IAAAoF,UAAA,GAAA1C,OAAA,CAAAhL,UAAA,CAAA2N,OAAA;YACA,IAAAC,uBAAA,GAAA5C,OAAA,CAAA6C,qBAAA,CAAAH,UAAA;;YAEA;YACA1C,OAAA,CAAAlC,mBAAA,GAAAkC,OAAA,CAAA8C,0BAAA,CAAAF,uBAAA;YACA;YACA5C,OAAA,CAAA7L,eAAA,GAAA6L,OAAA,CAAA+C,uBAAA,CAAAH,uBAAA;;YAEA;YACA5C,OAAA,CAAAgD,iBAAA;YACAhD,OAAA,CAAAiD,WAAA;UACA;QACA;;QAEA;QACAjD,OAAA,CAAAhL,UAAA,CAAA4M,EAAA;UACA5B,OAAA,CAAA9K,iBAAA;UACA;UACA,IAAAgO,aAAA,GAAAlD,OAAA,CAAAlC,mBAAA,IAAAkC,OAAA,CAAA7L,eAAA;UACA,IAAA+O,aAAA;YACAlD,OAAA,CAAAhL,UAAA,CAAAyH,OAAA,CAAAyG,aAAA;UACA;QACA;MACA;IAEA,SAAA1M,KAAA;MAEA;MACA,KAAA0J,kBAAA;IACA;EACA,mCAGAA,mBAAA;IAAA,IAAAiD,OAAA;IACA,IAAAhD,eAAA,GAAAC,QAAA,CAAAC,cAAA;IACA,IAAAF,eAAA;MACA,IAAAiD,QAAA,GAAAhD,QAAA,CAAAiD,aAAA;MACAD,QAAA,CAAAE,SAAA;MACAF,QAAA,CAAAG,WAAA;MACAH,QAAA,CAAAI,KAAA,QAAArP,eAAA;MACAiP,QAAA,CAAAK,KAAA,CAAAC,OAAA;;MAEA;MACAN,QAAA,CAAAxN,gBAAA,oBAAA2M,CAAA;QACAY,OAAA,CAAAhP,eAAA,GAAAoO,CAAA,CAAAoB,MAAA,CAAAH,KAAA;QACAL,OAAA,CAAArF,mBAAA,GAAAyE,CAAA,CAAAoB,MAAA,CAAAH,KAAA;QACAL,OAAA,CAAApF,YAAA,OAAAC,IAAA,GAAAC,cAAA;;QAEA;QACAkF,OAAA,CAAAH,iBAAA;QACAG,OAAA,CAAAF,WAAA;MACA;MAEA9C,eAAA,CAAAG,SAAA;MACAH,eAAA,CAAAyD,WAAA,CAAAR,QAAA;MACA,KAAAlO,iBAAA;IACA;EACA,8BAGA2O,cAAAC,IAAA;IACA,IAAAC,GAAA,GAAA3D,QAAA,CAAAiD,aAAA;IACAU,GAAA,CAAAzD,SAAA,GAAAwD,IAAA;IACA,OAAAC,GAAA,CAAAC,WAAA,IAAAD,GAAA,CAAAE,SAAA;EACA,iCAGApG,iBAAAqG,OAAA;IAEA,SAAAlP,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAAyH,OAAA,CAAAyH,OAAA;IACA;MACA;MACA,KAAA/P,eAAA,GAAA+P,OAAA;MACA,KAAApG,mBAAA,GAAAoG,OAAA;IACA;EACA,yBAKA7O,SAAA8O,IAAA,EAAAC,IAAA;IACA,IAAAC,OAAA;IACA,gBAAAC,iBAAA;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAA7L,MAAA,EAAA8L,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;MAAA;MACA,IAAAC,KAAA,YAAAA,MAAA;QACA3O,YAAA,CAAAoO,OAAA;QACAF,IAAA,CAAAU,KAAA,SAAAJ,IAAA;MACA;MACAxO,YAAA,CAAAoO,OAAA;MACAA,OAAA,GAAA/G,UAAA,CAAAsH,KAAA,EAAAR,IAAA;IACA;EACA,sCAGAvB,sBAAAqB,OAAA;IACA,KAAAA,OAAA,SAAAA,OAAA;;IAEA;IACA,IAAAY,aAAA,GAAAnP,MAAA,CAAAoP,QAAA,CAAAC,MAAA;IACA,IAAAC,QAAA,OAAAC,MAAA,CAAAJ,aAAA,CAAA3J,OAAA;IAEA,OAAA+I,OAAA,CAAA/I,OAAA,CAAA8J,QAAA;EACA,8BAGA3P,cAAA;IACA,UAAAnB,eAAA,CAAAI,IAAA;MACA,KAAAE,eAAA;MACA,KAAAC,WAAA;MACA;IACA;IAEA;MACA,IAAAyQ,WAAA,QAAAC,oBAAA,MAAAjR,eAAA;MACA;MACA,KAAAM,eAAA,GAAA0Q,WAAA,CAAA5H,SAAA,CAAAtE,GAAA,WAAAwB,QAAA;QAAA,WAAAlD,cAAA,CAAA/D,OAAA,MAAA+D,cAAA,CAAA/D,OAAA,MACAiH,QAAA;UACA+C,SAAA;QAAA;MAAA,CACA;MACA,KAAA9I,WAAA,GAAAyQ,WAAA,CAAAzH,MAAA;;MAEA;MACA,KAAAK,YAAA,OAAAC,IAAA,GAAAC,cAAA;IACA,SAAAzH,KAAA;MAEA,KAAA9B,WAAA,cAAA8B,KAAA,CAAA6O,OAAA;MACA,KAAA5Q,eAAA;IACA;EACA,QAAAlB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,mCAGAqT,qBAAAlB,OAAA;IACA,IAAA3G,SAAA;IACA,IAAAG,MAAA;IAEA,KAAAwG,OAAA,WAAAA,OAAA;MAEA;QAAA3G,SAAA,EAAAA,SAAA;QAAAG,MAAA;MAAA;IACA;IAEA;MAGA;MACA,IAAAsG,WAAA,QAAAjB,uBAAA,CAAAmB,OAAA;MAEA,KAAAF,WAAA,IAAAA,WAAA,CAAAzP,IAAA,GAAAoE,MAAA;QAEA;UAAA4E,SAAA,EAAAA,SAAA;UAAAG,MAAA;QAAA;MACA;;MAEA;MACA,IAAA4H,KAAA,GAAAtB,WAAA,CAAAuB,KAAA,OAAAtM,GAAA,WAAAuM,IAAA;QAAA,OAAAA,IAAA,CAAAjR,IAAA;MAAA,GAAAkR,MAAA,WAAAD,IAAA;QAAA,OAAAA,IAAA,CAAA7M,MAAA;MAAA;MAGA,IAAA2M,KAAA,CAAA3M,MAAA;QAEA;UAAA4E,SAAA,EAAAA,SAAA;UAAAG,MAAA;QAAA;MACA;MAIA,IAAAgI,oBAAA;MACA,IAAAC,cAAA;MAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAA3M,MAAA,EAAAiN,CAAA;QACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;;QAEA;QACA,IAAAC,eAAA,QAAAC,mBAAA,CAAAN,IAAA,UAAAO,mBAAA,CAAAP,IAAA;QAEA,IAAAK,eAAA;UACA;UACA,IAAAH,oBAAA,CAAA/M,MAAA;YACA;cACA,IAAAqN,YAAA,GAAAN,oBAAA,CAAAO,IAAA;cACA,IAAAC,cAAA,QAAAC,sBAAA,CAAAH,YAAA,EAAAL,cAAA;cACA,IAAAO,cAAA;gBACA3I,SAAA,CAAApD,IAAA,CAAA+L,cAAA;cACA;YACA,SAAA1P,KAAA;cACAkH,MAAA,CAAAvD,IAAA,WAAArB,MAAA,CAAA6M,cAAA,uCAAA7M,MAAA,CAAAtC,KAAA,CAAA6O,OAAA;cACAe,OAAA,CAAA5P,KAAA,wBAAAsC,MAAA,CAAA6M,cAAA,iCAAAnP,KAAA;YACA;UACA;;UAEA;UACAkP,oBAAA,IAAAF,IAAA;UACAG,cAAA;QACA;UACA;UACA,IAAAD,oBAAA,CAAA/M,MAAA;YACA+M,oBAAA,CAAAvL,IAAA,CAAAqL,IAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAE,oBAAA,CAAA/M,MAAA;QACA;UACA,IAAAqN,aAAA,GAAAN,oBAAA,CAAAO,IAAA;UACA,IAAAC,eAAA,QAAAC,sBAAA,CAAAH,aAAA,EAAAL,cAAA;UACA,IAAAO,eAAA;YACA3I,SAAA,CAAApD,IAAA,CAAA+L,eAAA;UACA;QACA,SAAA1P,KAAA;UACAkH,MAAA,CAAAvD,IAAA,WAAArB,MAAA,CAAA6M,cAAA,uCAAA7M,MAAA,CAAAtC,KAAA,CAAA6O,OAAA;UACAe,OAAA,CAAA5P,KAAA,oCAAAsC,MAAA,CAAA6M,cAAA,iCAAAnP,KAAA;QACA;MACA;IAEA,SAAAA,KAAA;MACAkH,MAAA,CAAAvD,IAAA,0CAAArB,MAAA,CAAAtC,KAAA,CAAA6O,OAAA;MACAe,OAAA,CAAA5P,KAAA,cAAAA,KAAA;IACA;IAEA4P,OAAA,CAAAC,GAAA,WAAA9I,SAAA,CAAA5E,MAAA,UAAA+E,MAAA,CAAA/E,MAAA;IACA;MAAA4E,SAAA,EAAAA,SAAA;MAAAG,MAAA,EAAAA;IAAA;EACA,oCAGAoI,oBAAAN,IAAA;IACA;IACA;IACA;IACA,wBAAAc,IAAA,CAAAd,IAAA;EACA,oCAGAO,oBAAAP,IAAA;IACA;IACA;IACA,mBAAAc,IAAA,CAAAd,IAAA;EACA,uCAGAW,uBAAAH,YAAA;IACA,IAAAV,KAAA,GAAAU,YAAA,CAAAT,KAAA,OAAAtM,GAAA,WAAAuM,IAAA;MAAA,OAAAA,IAAA,CAAAjR,IAAA;IAAA,GAAAkR,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAA7M,MAAA;IAAA;IAEA,IAAA2M,KAAA,CAAA3M,MAAA;MACA,UAAAkH,KAAA;IACA;IAEA,IAAA3M,YAAA;IACA,IAAAE,eAAA;IACA,IAAAmT,iBAAA;;IAEA;IACA,SAAAX,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAA3M,MAAA,EAAAiN,CAAA;MACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;MACA,IAAAY,SAAA,GAAAhB,IAAA,CAAAiB,KAAA;MACA,IAAAD,SAAA;QACA,IAAAE,QAAA,GAAAF,SAAA;;QAEA;QACA,IAAAE,QAAA,CAAAxM,QAAA;UACAhH,YAAA;QACA,WAAAwT,QAAA,CAAAxM,QAAA;UACAhH,YAAA;QACA,WAAAwT,QAAA,CAAAxM,QAAA;UACAhH,YAAA;QACA,WAAAwT,QAAA,CAAAxM,QAAA;UACAhH,YAAA;QACA,WAAAwT,QAAA,CAAAxM,QAAA;UACAhH,YAAA;QACA;;QAEA;QACA,IAAAyT,gBAAA,GAAAnB,IAAA,CAAArK,OAAA,iBAAA5G,IAAA;QACA,IAAAoS,gBAAA;UACAvT,eAAA,GAAAuT,gBAAA;UACAJ,iBAAA,GAAAX,CAAA;QACA;UACAW,iBAAA,GAAAX,CAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,IAAAW,iBAAA;MACAA,iBAAA;IACA;;IAEA;IACA,SAAAX,EAAA,GAAAW,iBAAA,EAAAX,EAAA,GAAAN,KAAA,CAAA3M,MAAA,EAAAiN,EAAA;MACA,IAAAJ,KAAA,GAAAF,KAAA,CAAAM,EAAA;;MAEA;MACA,SAAAE,mBAAA,CAAAN,KAAA;QACA;QACApS,eAAA,GAAAoS,KAAA,CAAArK,OAAA,uBAAA5G,IAAA;QACAgS,iBAAA,GAAAX,EAAA;QACA;MACA,YAAAxS,eAAA;QACA;QACAA,eAAA,GAAAoS,KAAA;QACAe,iBAAA,GAAAX,EAAA;QACA;MACA;IACA;;IAEA;IACA,SAAAA,GAAA,GAAAW,iBAAA,EAAAX,GAAA,GAAAN,KAAA,CAAA3M,MAAA,EAAAiN,GAAA;MACA,IAAAJ,MAAA,GAAAF,KAAA,CAAAM,GAAA;;MAEA;MACA,SAAAgB,YAAA,CAAApB,MAAA,UAAAqB,YAAA,CAAArB,MAAA,KACA,KAAAsB,iBAAA,CAAAtB,MAAA,UAAAuB,gBAAA,CAAAvB,MAAA;QACA;MACA;;MAEA;MACA,IAAAwB,SAAA,GAAAxB,MAAA;MACA;MACA,SAAAM,mBAAA,CAAAN,MAAA;QACAwB,SAAA,GAAAxB,MAAA,CAAArK,OAAA,uBAAA5G,IAAA;MACA;MAEA,IAAAyS,SAAA;QACA,IAAA5T,eAAA;UACAA,eAAA,WAAA4T,SAAA;QACA;UACA5T,eAAA,GAAA4T,SAAA;QACA;MACA;IACA;IAEA,KAAA5T,eAAA;MACA,UAAAyM,KAAA;IACA;;IAEA;IACA,IAAAoH,oBAAA,GAAA7T,eAAA,CAAAmB,IAAA;IACA;IACA,wBAAA+R,IAAA,CAAAW,oBAAA;MACAA,oBAAA,GAAAA,oBAAA,CAAA9L,OAAA,0BAAA5G,IAAA;IACA;;IAEA;IACA,IAAA0S,oBAAA,CAAA/M,QAAA;MACA+M,oBAAA,QAAAC,oBAAA,CAAAD,oBAAA;IACA;IAEA,IAAAxM,QAAA;MACAvH,YAAA,EAAAA,YAAA;MACAkF,IAAA,EAAAlF,YAAA;MACAiU,QAAA,OAAAC,kBAAA,CAAAlU,YAAA;MACAE,eAAA,EAAA6T,oBAAA;MACA/C,OAAA,EAAA+C,oBAAA;MACA9T,UAAA;MAAA;MACAkU,WAAA;MACAC,OAAA;MACAC,aAAA;MACA/J,SAAA;IACA;;IAEA;IACA,IAAAgK,YAAA,QAAAC,qBAAA,CAAAnC,KAAA;IACA7K,QAAA,CAAA6M,OAAA,GAAAE,YAAA,CAAAF,OAAA;;IAEA;IACA,IAAApU,YAAA,mBAAAuH,QAAA,CAAA6M,OAAA,CAAA3O,MAAA;MACA;MACAzF,YAAA;MACAuH,QAAA,CAAAvH,YAAA,GAAAA,YAAA;MACAuH,QAAA,CAAArC,IAAA,GAAAlF,YAAA;MACAuH,QAAA,CAAA0M,QAAA,QAAAC,kBAAA,CAAAlU,YAAA;IACA;;IAEA;IACA,KAAAwU,0BAAA,CAAApC,KAAA,EAAA7K,QAAA;;IAEA;IACA,IAAAvH,YAAA,iBAAAuH,QAAA,CAAA8M,aAAA,IAAA9M,QAAA,CAAA8M,aAAA,CAAA5O,MAAA;MACA;MACA,kBAAA2N,IAAA,CAAA7L,QAAA,CAAA8M,aAAA;QACArU,YAAA;QACAuH,QAAA,CAAAvH,YAAA,GAAAA,YAAA;QACAuH,QAAA,CAAArC,IAAA,GAAAlF,YAAA;QACAuH,QAAA,CAAA0M,QAAA,QAAAC,kBAAA,CAAAlU,YAAA;MACA;IACA;;IAEA;IACAuH,QAAA,CAAArH,eAAA,QAAA8T,oBAAA,CAAAzM,QAAA,CAAArH,eAAA;IACAqH,QAAA,CAAAyJ,OAAA,GAAAzJ,QAAA,CAAArH,eAAA;IAEA,OAAAqH,QAAA;EACA,6BAGAmM,aAAApB,IAAA;IACA;IACA,6BAAAc,IAAA,CAAAd,IAAA;EACA,6BAGAqB,aAAArB,IAAA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA;EACA,kCAGAsB,kBAAAtB,IAAA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA;EACA,iCAGAuB,iBAAAvB,IAAA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA;EACA,mCAGA4B,mBAAAhP,IAAA;IACA,IAAAZ,OAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAY,IAAA;EACA,kCAGAuP,kBAAAzD,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MAEA;MACA,IAAA0D,gBAAA,GAAA1D,OAAA,CAAA/I,OAAA,mDAAAsL,KAAA,EAAAoB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,SAAArB,KAAA;;QAGA;QACA,IAAAqB,GAAA,CAAA1F,UAAA,eAAA0F,GAAA,CAAA1F,UAAA,gBAAA0F,GAAA,CAAA1F,UAAA;UACA,OAAAqE,KAAA;QACA;;QAEA;QACA,IAAAuB,OAAA,8BAAAF,GAAA,CAAA1F,UAAA,QAAA0F,GAAA,SAAAA,GAAA;QACA,IAAAG,MAAA,UAAAnP,MAAA,CAAA+O,MAAA,YAAA/O,MAAA,CAAAkP,OAAA,QAAAlP,MAAA,CAAAiP,KAAA;QACA,OAAAE,MAAA;MACA;MAEA,OAAAL,gBAAA;IACA,SAAApR,KAAA;MACA4P,OAAA,CAAA5P,KAAA,iBAAAA,KAAA;MACA,OAAA0N,OAAA;IACA;EACA,QAAA3Q,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,yCAGA+Q,2BAAAoB,OAAA;IAAA,IAAAgE,OAAA;IACA,KAAAhE,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MACA;MACA,IAAA0D,gBAAA,GAAA1D;MACA;MAAA,CACA/I,OAAA,oDAAAsL,KAAA,EAAAoB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,CAAA1F,UAAA,aAAA0F,GAAA,CAAA1F,UAAA;UACA,IAAA4F,OAAA,GAAAE,OAAA,CAAAP,iBAAA,CAAAG,GAAA;UACA,cAAAhP,MAAA,CAAA+O,MAAA,YAAA/O,MAAA,CAAAkP,OAAA,QAAAlP,MAAA,CAAAiP,KAAA;QACA;QACA,OAAAtB,KAAA;MACA;MACA;MAAA,CACAtL,OAAA,sBACAA,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACAA,OAAA,sBACAA,OAAA;MAEA,OAAAyM,gBAAA,CAAArT,IAAA;IACA,SAAAiC,KAAA;MACA4P,OAAA,CAAA5P,KAAA,qCAAAA,KAAA;MACA,OAAA0N,OAAA;IACA;EACA,wCAGAnB,wBAAAmB,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MAEA;MACA,IAAAiE,MAAA;MACA,IAAAC,UAAA;MACA,IAAAC,uBAAA,GAAAnE,OAAA,CAAA/I,OAAA,2BAAAsL,KAAA;QACA0B,MAAA,CAAAhO,IAAA,CAAAsM,KAAA;QACA,gCAAA3N,MAAA,CAAAsP,UAAA;MACA;;MAEA;MACA,IAAApE,WAAA,GAAAqE,uBAAA,CACAlN,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;;MAEA;MACA,IAAAmN,YAAA,GAAAtE,WAAA;MACAmE,MAAA,CAAAvQ,OAAA,WAAA2Q,GAAA,EAAAnO,KAAA;QACA,IAAAmJ,WAAA,0BAAAzK,MAAA,CAAAsB,KAAA;QACA,IAAAkO,YAAA,CAAApO,QAAA,CAAAqJ,WAAA;UACA+E,YAAA,GAAAA,YAAA,CAAAnN,OAAA,CAAAoI,WAAA,EAAAgF,GAAA;QACA;MACA;MAEA,OAAAD,YAAA,CAAA/T,IAAA;IACA,SAAAiC,KAAA;MACA4P,OAAA,CAAA5P,KAAA,kCAAAA,KAAA;MACA,OAAA0N,OAAA;IACA;EACA,sCAGAuD,sBAAAnC,KAAA,EAAAkD,UAAA;IACA,IAAAlB,OAAA;IAEA,KAAA5C,KAAA,CAAA+D,OAAA,CAAAnD,KAAA,KAAAkD,UAAA,QAAAA,UAAA,IAAAlD,KAAA,CAAA3M,MAAA;MACAyN,OAAA,CAAAsC,IAAA;MACA;QAAApB,OAAA,EAAAA;MAAA;IACA;IAEA;MACA,SAAA1B,CAAA,GAAA4C,UAAA,EAAA5C,CAAA,GAAAN,KAAA,CAAA3M,MAAA,EAAAiN,CAAA;QACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;QAEA,KAAAJ,IAAA,WAAAA,IAAA;UACA;QACA;;QAEA;QACA,IAAAmD,WAAA,GAAAnD,IAAA,CAAAiB,KAAA;QACA,IAAAkC,WAAA;UACA,IAAAC,SAAA,GAAAD,WAAA,IAAAE,WAAA;UACA,IAAAC,aAAA,GAAAH,WAAA,MAAAA,WAAA,IAAApU,IAAA;UAEA,IAAAqU,SAAA,IAAAE,aAAA;YACAxB,OAAA,CAAAnN,IAAA;cACAyO,SAAA,EAAAA,SAAA;cACAG,KAAA,EAAAH,SAAA;cACAE,aAAA,EAAAA,aAAA;cACA5E,OAAA,EAAA4E;YACA;UACA;QACA,gBAAAjC,YAAA,CAAArB,IAAA,UAAAsB,iBAAA,CAAAtB,IAAA,UAAAuB,gBAAA,CAAAvB,IAAA;UACA;UACA;QACA;UACA;UACA;UACA,IAAAwD,oBAAA,GAAAxD,IAAA,CAAAiB,KAAA;UACA,IAAAuC,oBAAA;YACA;YACA,IAAAC,aAAA,GAAAzD,IAAA,CAAAD,KAAA;YAAA,IAAA2D,SAAA,OAAAC,2BAAA,CAAA3V,OAAA,EACAyV,aAAA;cAAAG,KAAA;YAAA;cAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAA3J,CAAA,IAAA9D,IAAA;gBAAA,IAAA6N,YAAA,GAAAF,KAAA,CAAA5F,KAAA;gBACA,KAAA8F,YAAA;gBAEA,IAAA7C,KAAA,GAAA6C,YAAA,CAAA7C,KAAA;gBACA,IAAAA,KAAA;kBACA,IAAAmC,UAAA,GAAAnC,KAAA,IAAAoC,WAAA;kBACA,IAAAC,cAAA,GAAArC,KAAA,MAAAA,KAAA,IAAAlS,IAAA;kBAEA,IAAAqU,UAAA,IAAAE,cAAA;oBACAxB,OAAA,CAAAnN,IAAA;sBACAyO,SAAA,EAAAA,UAAA;sBACAG,KAAA,EAAAH,UAAA;sBACAE,aAAA,EAAAA,cAAA;sBACA5E,OAAA,EAAA4E;oBACA;kBACA;gBACA;cACA;YAAA,SAAAS,GAAA;cAAAL,SAAA,CAAA3G,CAAA,CAAAgH,GAAA;YAAA;cAAAL,SAAA,CAAAM,CAAA;YAAA;UACA;QACA;MACA;IACA,SAAAhT,KAAA;MACA4P,OAAA,CAAA5P,KAAA,eAAAA,KAAA;IACA;IAEA;MAAA8Q,OAAA,EAAAA;IAAA;EACA,2CAGAI,2BAAApC,KAAA,EAAA7K,QAAA;IACA,SAAAmL,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAA3M,MAAA,EAAAiN,CAAA;MACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;;MAEA;MACA,IAAA6D,WAAA,GAAAjE,IAAA,CAAAiB,KAAA;MACA,IAAAgD,WAAA;QACAhP,QAAA,CAAA8M,aAAA,QAAAmC,gBAAA,CAAAD,WAAA,KAAAhP,QAAA,CAAAvH,YAAA;QACA;MACA;;MAEA;MACA,IAAAyW,gBAAA,GAAAnE,IAAA,CAAAiB,KAAA;MACA,IAAAkD,gBAAA;QACAlP,QAAA,CAAA4M,WAAA,GAAAsC,gBAAA,IAAApV,IAAA;QACA;MACA;;MAEA;MACA,IAAAqV,eAAA,GAAApE,IAAA,CAAAiB,KAAA;MACA,IAAAmD,eAAA;QACA,IAAAzW,UAAA,GAAAyW,eAAA;QACA;QACA,IAAAzW,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAA+G,QAAA,CAAA/G,UAAA;UACAsH,QAAA,CAAAtH,UAAA,GAAAA,UAAA;QACA;UACAiT,OAAA,CAAAsC,IAAA,iBAAAvV,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA;IACA,KAAAsH,QAAA,CAAA8M,aAAA;MACA9M,QAAA,CAAA8M,aAAA,QAAAsC,gCAAA,CAAApP,QAAA,CAAArH,eAAA,EAAAqH,QAAA,CAAAvH,YAAA;IACA;EACA,iDAGA2W,iCAAAzW,eAAA,EAAAF,YAAA;IACA,KAAAE,eAAA,WAAAA,eAAA;MACA;IACA;IAEA;MACA;MACA,IAAA0W,QAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;MAEA,SAAAC,GAAA,MAAAC,SAAA,GAAAF,QAAA,EAAAC,GAAA,GAAAC,SAAA,CAAArR,MAAA,EAAAoR,GAAA;QAAA,IAAAE,OAAA,GAAAD,SAAA,CAAAD,GAAA;QACA,IAAAG,OAAA,GAAA9W,eAAA,CAAAqT,KAAA,CAAAwD,OAAA;QACA,IAAAC,OAAA,IAAAA,OAAA,CAAAvR,MAAA;UACA;UACA,IAAAwR,SAAA,GAAAD,OAAA,CAAAA,OAAA,CAAAvR,MAAA;UACA,IAAAyR,MAAA,GAAAD,SAAA,CAAAhP,OAAA,sBAAA5G,IAAA;UAEA,IAAA6V,MAAA;YACA,YAAAV,gBAAA,CAAAU,MAAA,EAAAlX,YAAA;UACA;QACA;MACA;IACA,SAAAsD,KAAA;MACA4P,OAAA,CAAA5P,KAAA,kBAAAA,KAAA;IACA;IAEA;EACA,iCAGAkT,iBAAAW,UAAA,EAAAnX,YAAA;IACA,KAAAmX,UAAA,WAAAA,UAAA;MACA;IACA;IAEA;MACA,IAAAC,aAAA,GAAAD,UAAA,CAAA9V,IAAA;MAEA,KAAA+V,aAAA;QACA;MACA;MAEA,IAAApX,YAAA;QACA;QACA,OAAAoX,aAAA;MACA;QACA;QACA,OAAAA,aAAA,CAAAzB,WAAA;MACA;IACA,SAAArS,KAAA;MACA4P,OAAA,CAAA5P,KAAA,gBAAAA,KAAA;MACA,OAAA6T,UAAA;IACA;EACA,oCAGAE,oBAAArG,OAAA;IACA,IAAAsG,QAAA;IACA,IAAAC,SAAA;IAEA,IAAAC,SAAA;IACA,IAAAjE,KAAA;IACA,IAAAkE,WAAA;IAEA,QAAAlE,KAAA,GAAAgE,SAAA,CAAAG,IAAA,CAAA1G,OAAA;MACA,IAAAyG,WAAA;QACA;QACAH,QAAA,CAAArQ,IAAA;UACA/B,IAAA,EAAAuS,WAAA;UACAzG,OAAA,EAAAA,OAAA,CAAA7I,SAAA,CAAAqP,SAAA,EAAAjE,KAAA,CAAArM,KAAA,EAAA7F,IAAA;QACA;MACA;MACAoW,WAAA,GAAAlE,KAAA;MACAiE,SAAA,GAAAjE,KAAA,CAAArM,KAAA,GAAAqM,KAAA,IAAA9N,MAAA;IACA;;IAEA;IACA,IAAAgS,WAAA;MACAH,QAAA,CAAArQ,IAAA;QACA/B,IAAA,EAAAuS,WAAA;QACAzG,OAAA,EAAAA,OAAA,CAAA7I,SAAA,CAAAqP,SAAA,EAAAnW,IAAA;MACA;IACA;IAEA,OAAAiW,QAAA;EACA,sCAGAK,sBAAAC,OAAA;IAAA,IAAAC,OAAA;IACA,IAAAxN,SAAA;IACA,IAAArK,YAAA,QAAA8X,mBAAA,CAAAF,OAAA,CAAA1S,IAAA;;IAEA;IACA,IAAA6S,cAAA,QAAAC,qBAAA,CAAAJ,OAAA,CAAA5G,OAAA;IAEA+G,cAAA,CAAArT,OAAA,WAAAuT,KAAA,EAAA/Q,KAAA;MACA;QACA,IAAAK,QAAA,GAAAsQ,OAAA,CAAAK,kBAAA,CAAAD,KAAA,EAAAjY,YAAA,EAAAkH,KAAA;QACA,IAAAK,QAAA;UACA8C,SAAA,CAAApD,IAAA,CAAAM,QAAA;QACA;MACA,SAAAjE,KAAA;QACA,UAAAqJ,KAAA,UAAA/G,MAAA,CAAAsB,KAAA,0CAAAtB,MAAA,CAAAtC,KAAA,CAAA6O,OAAA;MACA;IACA;IAEA,OAAA9H,SAAA;EACA,sCAGA2N,sBAAAhH,OAAA;IACA,IAAAmH,MAAA;IACA,IAAAC,WAAA;IAEA,IAAAZ,SAAA;IACA,IAAAjE,KAAA;IAEA,QAAAA,KAAA,GAAA6E,WAAA,CAAAV,IAAA,CAAA1G,OAAA;MACA,IAAAwG,SAAA;QACA;QACAW,MAAA,CAAAlR,IAAA,CAAA+J,OAAA,CAAA7I,SAAA,CAAAqP,SAAA,EAAAjE,KAAA,CAAArM,KAAA,EAAA7F,IAAA;MACA;MACAmW,SAAA,GAAAjE,KAAA,CAAArM,KAAA;IACA;;IAEA;IACA,IAAAsQ,SAAA,GAAAxG,OAAA,CAAAvL,MAAA;MACA0S,MAAA,CAAAlR,IAAA,CAAA+J,OAAA,CAAA7I,SAAA,CAAAqP,SAAA,EAAAnW,IAAA;IACA;IAEA,OAAA8W,MAAA,CAAA5F,MAAA,WAAA0F,KAAA;MAAA,OAAAA,KAAA,CAAAxS,MAAA;IAAA;EACA,mCAGAyS,mBAAAD,KAAA,EAAAjY,YAAA;IACA,IAAAoS,KAAA,GAAA6F,KAAA,CAAA5F,KAAA,OAAAtM,GAAA,WAAAuM,IAAA;MAAA,OAAAA,IAAA,CAAAjR,IAAA;IAAA,GAAAkR,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAA7M,MAAA;IAAA;IAEA,IAAA2M,KAAA,CAAA3M,MAAA;MACA,UAAAkH,KAAA;IACA;;IAEA;IACA,IAAA0L,SAAA,GAAAjG,KAAA;IACA,IAAAlS,eAAA;IACA,IAAAoY,gBAAA;;IAEA;IACA,IAAAC,WAAA,GAAAF,SAAA,CAAA9E,KAAA;IACA,IAAAgF,WAAA;MACArY,eAAA,GAAAqY,WAAA,IAAAlX,IAAA;MACAiX,gBAAA;IACA;MACA;MACApY,eAAA,QAAA8T,oBAAA,CAAAqE,SAAA,EAAAhX,IAAA;MACAiX,gBAAA;IACA;;IAEA;IACA,OAAAA,gBAAA,GAAAlG,KAAA,CAAA3M,MAAA;MACA,IAAA6M,IAAA,GAAAF,KAAA,CAAAkG,gBAAA;MACA,SAAA5E,YAAA,CAAApB,IAAA;QACA;MACA;MACApS,eAAA,WAAAoS,IAAA;MACAgG,gBAAA;IACA;IAEA,IAAA/Q,QAAA;MACAvH,YAAA,EAAAA,YAAA;MACAE,eAAA,EAAAA,eAAA,CAAAmB,IAAA;MACApB,UAAA;MAAA;MACAkU,WAAA;MACAC,OAAA;MACAC,aAAA;IACA;;IAEA;IACA,IAAArU,YAAA;MACA,IAAAsU,YAAA,QAAAkE,YAAA,CAAApG,KAAA,EAAAkG,gBAAA;MACA/Q,QAAA,CAAA6M,OAAA,GAAAE,YAAA,CAAAF,OAAA;MACAkE,gBAAA,GAAAhE,YAAA,CAAAmE,SAAA;IACA;;IAEA;IACA,KAAAC,iBAAA,CAAAtG,KAAA,EAAAkG,gBAAA,EAAA/Q,QAAA;;IAEA;IACAA,QAAA,CAAArH,eAAA,QAAA8T,oBAAA,CAAAzM,QAAA,CAAArH,eAAA;IAEA,OAAAqH,QAAA;EACA,QAAAlH,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,2BAAA6U,aAGApB,IAAA;IACA,4BAAAc,IAAA,CAAAd,IAAA;EACA,6BAGAkG,aAAApG,KAAA,EAAAkD,UAAA;IACA,IAAAlB,OAAA;IACA,IAAAuE,YAAA,GAAArD,UAAA;IAEA,OAAAqD,YAAA,GAAAvG,KAAA,CAAA3M,MAAA;MACA,IAAA6M,IAAA,GAAAF,KAAA,CAAAuG,YAAA;MACA,IAAAlD,WAAA,GAAAnD,IAAA,CAAAiB,KAAA;MAEA,KAAAkC,WAAA;QACA;MACA;MAEArB,OAAA,CAAAnN,IAAA;QACAyO,SAAA,EAAAD,WAAA,IAAAE,WAAA;QACAC,aAAA,EAAAH,WAAA,IAAApU,IAAA;MACA;MAEAsX,YAAA;IACA;IAEA;MAAAvE,OAAA,EAAAA,OAAA;MAAAqE,SAAA,EAAAE;IAAA;EACA,kCAGAD,kBAAAtG,KAAA,EAAAkD,UAAA,EAAA/N,QAAA;IACA,SAAAmL,CAAA,GAAA4C,UAAA,EAAA5C,CAAA,GAAAN,KAAA,CAAA3M,MAAA,EAAAiN,CAAA;MACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;;MAEA;MACA,IAAA6D,WAAA,GAAAjE,IAAA,CAAAiB,KAAA;MACA,IAAAgD,WAAA;QACAhP,QAAA,CAAA8M,aAAA,QAAAuE,WAAA,CAAArC,WAAA,KAAAhP,QAAA,CAAAvH,YAAA;QACA;MACA;;MAEA;MACA,IAAAyW,gBAAA,GAAAnE,IAAA,CAAAiB,KAAA;MACA,IAAAkD,gBAAA;QACAlP,QAAA,CAAA4M,WAAA,GAAAsC,gBAAA,IAAApV,IAAA;QACA;MACA;;MAEA;MACA,IAAAqV,eAAA,GAAApE,IAAA,CAAAiB,KAAA;MACA,IAAAmD,eAAA;QACA,IAAAzW,UAAA,GAAAyW,eAAA;QACA;QACA,IAAAzW,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAA+G,QAAA,CAAA/G,UAAA;UACAsH,QAAA,CAAAtH,UAAA,GAAAA,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,KAAAsH,QAAA,CAAA8M,aAAA;MACA9M,QAAA,CAAA8M,aAAA,QAAAwE,wBAAA,CAAAtR,QAAA,CAAArH,eAAA,EAAAqH,QAAA,CAAAvH,YAAA;IACA;EACA,yCAKA6Y,yBAAA7H,OAAA,EAAAhR,YAAA;IACA;IACA,IAAA8Y,eAAA,IACA,cACA,iBACA,cACA,eACA;IAEA,SAAAC,GAAA,MAAAC,gBAAA,GAAAF,eAAA,EAAAC,GAAA,GAAAC,gBAAA,CAAAvT,MAAA,EAAAsT,GAAA;MAAA,IAAAhC,OAAA,GAAAiC,gBAAA,CAAAD,GAAA;MACA,IAAA/B,OAAA,OAAAzK,mBAAA,CAAAjM,OAAA,EAAA0Q,OAAA,CAAAiI,QAAA,CAAAlC,OAAA;MACA,IAAAC,OAAA,CAAAvR,MAAA;QACA,IAAAyR,MAAA,GAAAF,OAAA,CAAAA,OAAA,CAAAvR,MAAA;QACA,YAAAmT,WAAA,CAAA1B,MAAA,EAAAlX,YAAA;MACA;IACA;IAEA;EACA,oCAGA8X,oBAAAtE,QAAA;IACA,IAAAlP,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAkP,QAAA;EACA,oCAGA0F,oBAAAhU,IAAA;IACA,IAAAZ,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAY,IAAA;EACA,qCAGAiU,qBAAAjU,IAAA;IACA,IAAAkU,QAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,QAAA,CAAAlU,IAAA;EACA,kCAKA1C,kBAAA;IACA;MACA,IAAA6W,UAAA,GAAAC,YAAA,CAAAC,OAAA,MAAAC,QAAA;MACA,IAAAH,UAAA;QACA,IAAAla,IAAA,GAAAsa,IAAA,CAAAC,KAAA,CAAAL,UAAA;;QAEA;QACA,KAAApY,eAAA,GAAA9B,IAAA,CAAA8B,eAAA;QACA,KAAA2J,mBAAA,GAAAzL,IAAA,CAAAyL,mBAAA;QACA,KAAAC,YAAA,GAAA1L,IAAA,CAAA0L,YAAA;;QAEA;QACA,SAAA5J,eAAA,SAAA2J,mBAAA;UACA,KAAAkF,iBAAA;QACA;MACA;IACA,SAAAxM,KAAA;MACA4P,OAAA,CAAA5P,KAAA,gBAAAA,KAAA;IACA;EACA,4BAGAyM,YAAA;IAAA,IAAA4J,OAAA;IACA;IACA,SAAA7W,aAAA;MACAC,YAAA,MAAAD,aAAA;IACA;IAEA,KAAAA,aAAA,GAAAsH,UAAA;MACAuP,OAAA,CAAA9W,cAAA;IACA;EACA,+BAGAA,eAAA;IACA;MACA,IAAA+W,UAAA;QACA3Y,eAAA,OAAAA,eAAA;QACA2J,mBAAA,OAAAA,mBAAA;QACAC,YAAA,OAAAA,YAAA,QAAAC,IAAA,GAAAC,cAAA;QACA8O,SAAA,EAAA/O,IAAA,CAAAgP,GAAA;MACA;MAEAR,YAAA,CAAAS,OAAA,MAAAP,QAAA,EAAAC,IAAA,CAAAO,SAAA,CAAAJ,UAAA;MACA,KAAA9J,iBAAA;IACA,SAAAxM,KAAA;MACA4P,OAAA,CAAA5P,KAAA,eAAAA,KAAA;IACA;EACA,QAAAjD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,yBAGA+N,WAAA;IACA;MACA0M,YAAA,CAAAW,UAAA,MAAAT,QAAA;MACA,KAAA1J,iBAAA;MACAoD,OAAA,CAAAC,GAAA;IACA,SAAA7P,KAAA;MACA4P,OAAA,CAAA5P,KAAA,cAAAA,KAAA;IACA;EACA,mCAGAX,mBAAAuX,KAAA;IACA,SAAApK,iBAAA;MACA;MACA,KAAAjN,cAAA;;MAEA;MACA,IAAAsP,OAAA;MACA+H,KAAA,CAAAC,WAAA,GAAAhI,OAAA;MACA,OAAAA,OAAA;IACA;EACA,2BAGAiI,WAAA;IACA,KAAAvX,cAAA;IACA,KAAAQ,QAAA,CAAA6C,OAAA;EACA,mCAGAmU,mBAAAC,OAAA;IAAA,IAAAC,OAAA;IACA,QAAAD,OAAA;MACA;QACA,KAAAjU,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACArB,IAAA;QACA,GAAAlB,IAAA;UACAuW,OAAA,CAAA3N,UAAA;UACA2N,OAAA,CAAAtZ,eAAA;UACAsZ,OAAA,CAAA3P,mBAAA;UACA2P,OAAA,CAAA1P,YAAA;;UAEA;UACA,IAAA0P,OAAA,CAAAzY,UAAA,IAAAyY,OAAA,CAAAvY,iBAAA;YACAuY,OAAA,CAAAzY,UAAA,CAAAyH,OAAA;UACA;UAEAgR,OAAA,CAAAlX,QAAA,CAAA6C,OAAA;QACA,GAAA/B,KAAA;UACA;QAAA,CACA;QACA;MACA;QACA,KAAAqW,WAAA;QACA;IACA;EACA,4BAGAA,YAAA;IACA,UAAA5P,mBAAA,UAAA3J,eAAA;MACA,KAAAoC,QAAA,CAAAqC,OAAA;MACA;IACA;IAEA,IAAAsL,OAAA,QAAApG,mBAAA,SAAA3J,eAAA;IACA,IAAAwZ,IAAA,OAAAC,IAAA,EAAA1J,OAAA;MAAA9L,IAAA;IAAA;IACA,IAAAyV,GAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAJ,IAAA;IACA,IAAAK,IAAA,GAAA5N,QAAA,CAAAiD,aAAA;IACA2K,IAAA,CAAAC,IAAA,GAAAJ,GAAA;IACAG,IAAA,CAAArR,QAAA,+BAAA7D,MAAA,KAAAkF,IAAA,GAAAkQ,WAAA,GAAAC,KAAA,QAAAhT,OAAA;IACAiF,QAAA,CAAAgO,IAAA,CAAAxK,WAAA,CAAAoK,IAAA;IACAA,IAAA,CAAAK,KAAA;IACAjO,QAAA,CAAAgO,IAAA,CAAAE,WAAA,CAAAN,IAAA;IACAF,GAAA,CAAAS,eAAA,CAAAV,GAAA;IAEA,KAAAtX,QAAA,CAAA6C,OAAA;EACA,4CAKAoV,4BAAA/T,QAAA;IACA,KAAAA,QAAA,KAAAA,QAAA,CAAArH,eAAA;MACA;IACA;IAEA,IAAA8Q,OAAA,GAAAzJ,QAAA,CAAArH,eAAA;;IAEA;IACA,SAAA0K,mBAAA,SAAAA,mBAAA,CAAA5D,QAAA;MACA;MACA,IAAAuU,WAAA,QAAAC,uBAAA,CAAAjU,QAAA,CAAArH,eAAA,OAAA0K,mBAAA;MACA,IAAA2Q,WAAA;QACAvK,OAAA,GAAAuK,WAAA;MACA;IACA;;IAEA;IACAvK,OAAA,QAAAgD,oBAAA,CAAAhD,OAAA;IAEA,YAAAyD,iBAAA,CAAAzD,OAAA;EACA,qCAGAgD,qBAAAhD,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA,OAAAA,OAAA;IACA;;IAEA;IACA,IAAAA,OAAA,CAAAhK,QAAA;MACA;MACA,OAAAgK,OAAA,CAAA/I,OAAA,wDACAA,OAAA;MAAA,CACAA,OAAA;IACA;MACA;MACA,OAAA+I,OAAA,CAAA/I,OAAA,0BAAA5G,IAAA;IACA;EACA,wCAGAma,wBAAAC,YAAA,EAAAF,WAAA;IACA,KAAAE,YAAA,KAAAF,WAAA;MACA,OAAAE,YAAA;IACA;IAEA;MACA;MACA,IAAAC,SAAA,GAAAD,YAAA,CAAAxT,OAAA,uBAAA5G,IAAA;;MAEA;MACA,IAAAsa,UAAA,GAAAJ,WAAA,CAAAhI,KAAA;MAAA,IAAAqI,UAAA,OAAA3F,2BAAA,CAAA3V,OAAA,EAEAqb,UAAA;QAAAE,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAzF,CAAA,MAAA0F,MAAA,GAAAD,UAAA,CAAAvP,CAAA,IAAA9D,IAAA;UAAA,IAAAuT,SAAA,GAAAD,MAAA,CAAAvL,KAAA;UACA,IAAAyL,aAAA,GAAAD,SAAA,CAAA7T,OAAA,iBAAA5G,IAAA;UACA;UACA,IAAA2a,kBAAA,GAAAD,aAAA,CAAA9T,OAAA,0BAAA5G,IAAA;UACA,IAAA2a,kBAAA,CAAAhV,QAAA,CAAA0U,SAAA,CAAAvT,SAAA;YACA;YACA,YAAA6L,oBAAA,CAAA8H,SAAA;UACA;QACA;;QAEA;MAAA,SAAAzF,GAAA;QAAAuF,UAAA,CAAAvM,CAAA,CAAAgH,GAAA;MAAA;QAAAuF,UAAA,CAAAtF,CAAA;MAAA;MACA,OAAAmF,YAAA;IACA,SAAAnY,KAAA;MACA4P,OAAA,CAAA5P,KAAA,kBAAAA,KAAA;MACA,OAAAmY,YAAA;IACA;EACA,6BAIAQ,aAAA;IACA,KAAApc,WAAA,CAAAC,OAAA;IACA,KAAA0D,eAAA;EACA,4BAEA0Y,YAAA;IACA,KAAArc,WAAA,CAAAG,YAAA;IACA,KAAAH,WAAA,CAAAI,UAAA;IACA,KAAAJ,WAAA,CAAAK,eAAA;IACA,KAAAL,WAAA,CAAAC,OAAA;IACA,KAAA0D,eAAA;EACA;AAEA", "ignoreList": []}]}