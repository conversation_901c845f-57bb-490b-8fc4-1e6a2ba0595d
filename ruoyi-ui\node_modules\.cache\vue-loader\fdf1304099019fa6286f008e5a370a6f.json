{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionCard.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionCard.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUXVlc3Rpb25DYXJkIiwKICBwcm9wczogewogICAgcXVlc3Rpb246IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIGluZGV4OiB7CiAgICAgIHR5cGU6IE51bWJlciwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBleHBhbmRlZDogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIHNlbGVjdGVkOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHt9CiAgfSwKICBjb21wdXRlZDogewogICAgaXNDaG9pY2VRdWVzdGlvbigpIHsKICAgICAgcmV0dXJuIFsnc2luZ2xlJywgJ211bHRpcGxlJywgMSwgMl0uaW5jbHVkZXModGhpcy5xdWVzdGlvbi5xdWVzdGlvblR5cGUpCiAgICB9LAogICAgaXNKdWRnbWVudFF1ZXN0aW9uKCkgewogICAgICByZXR1cm4gWydqdWRnbWVudCcsIDNdLmluY2x1ZGVzKHRoaXMucXVlc3Rpb24ucXVlc3Rpb25UeXBlKQogICAgfSwKICAgIC8vIOi/h+a7pOacieaViOeahOmAiemhuQogICAgdmFsaWRPcHRpb25zKCkgewogICAgICBsZXQgb3B0aW9ucyA9IHRoaXMucXVlc3Rpb24ub3B0aW9ucwoKICAgICAgLy8g5aaC5p6cb3B0aW9uc+aYr+Wtl+espuS4su+8jOmcgOimgeino+aekOS4ukpTT04KICAgICAgaWYgKHR5cGVvZiBvcHRpb25zID09PSAnc3RyaW5nJykgewogICAgICAgIHRyeSB7CiAgICAgICAgICBvcHRpb25zID0gSlNPTi5wYXJzZShvcHRpb25zKQogICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgIHJldHVybiBbXQogICAgICAgIH0KICAgICAgfQoKICAgICAgaWYgKCFvcHRpb25zIHx8ICFBcnJheS5pc0FycmF5KG9wdGlvbnMpKSB7CiAgICAgICAgcmV0dXJuIFtdCiAgICAgIH0KCiAgICAgIHJldHVybiBvcHRpb25zLmZpbHRlcihvcHRpb24gPT4gewogICAgICAgIC8vIOajgOafpemAiemhueaYr+WQpuacieaViAogICAgICAgIGNvbnN0IGtleSA9IG9wdGlvbi5rZXkgfHwgb3B0aW9uLm9wdGlvbktleQogICAgICAgIGNvbnN0IGNvbnRlbnQgPSBvcHRpb24uY29udGVudCB8fCBvcHRpb24ub3B0aW9uQ29udGVudAoKICAgICAgICBjb25zdCBoYXNLZXkgPSBrZXkgJiYga2V5LnRvU3RyaW5nKCkudHJpbSgpCiAgICAgICAgLy8g5YaF5a655Y+v5Lul5Li656m677yM5L2Ga2V55b+F6aG75a2Y5ZyoCiAgICAgICAgY29uc3QgaGFzVmFsaWRLZXkgPSBoYXNLZXkKICAgICAgICAvLyDlpoLmnpzlhoXlrrnkuLrnqbrmiJblj6rljIXlkKtIVE1M5qCH562+77yM5YiZ6L+H5ruk5o6JCiAgICAgICAgY29uc3QgaGFzVmFsaWRDb250ZW50ID0gY29udGVudCAmJiBjb250ZW50LnRvU3RyaW5nKCkudHJpbSgpICYmCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50LnRvU3RyaW5nKCkucmVwbGFjZSgvPFtePl0qPi9nLCAnJykudHJpbSgpCgogICAgICAgIHJldHVybiBoYXNWYWxpZEtleSAmJiBoYXNWYWxpZENvbnRlbnQKICAgICAgfSkKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOiOt+WPlumimOWei+WQjeensAogICAgZ2V0UXVlc3Rpb25UeXBlTmFtZSh0eXBlKSB7CiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7CiAgICAgICAgLy8g5a2X56ym5Liy5qC85byPCiAgICAgICAgJ3NpbmdsZSc6ICfljZXpgInpopgnLAogICAgICAgICdtdWx0aXBsZSc6ICflpJrpgInpopgnLAogICAgICAgICdqdWRnbWVudCc6ICfliKTmlq3popgnLAogICAgICAgIC8vIOaVsOWtl+agvOW8jwogICAgICAgIDE6ICfljZXpgInpopgnLAogICAgICAgIDI6ICflpJrpgInpopgnLAogICAgICAgIDM6ICfliKTmlq3popgnCiAgICAgIH0KICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZV0gfHwgJ+acquefpemimOWeiycKICAgIH0sCiAgICAvLyDojrflj5bpopjlnovpopzoibIKICAgIGdldFF1ZXN0aW9uVHlwZUNvbG9yKHR5cGUpIHsKICAgICAgY29uc3QgY29sb3JNYXAgPSB7CiAgICAgICAgLy8g5a2X56ym5Liy5qC85byPCiAgICAgICAgJ3NpbmdsZSc6ICdwcmltYXJ5JywKICAgICAgICAnbXVsdGlwbGUnOiAnc3VjY2VzcycsCiAgICAgICAgJ2p1ZGdtZW50JzogJ3dhcm5pbmcnLAogICAgICAgIC8vIOaVsOWtl+agvOW8jwogICAgICAgIDE6ICdwcmltYXJ5JywKICAgICAgICAyOiAnc3VjY2VzcycsCiAgICAgICAgMzogJ3dhcm5pbmcnCiAgICAgIH0KICAgICAgcmV0dXJuIGNvbG9yTWFwW3R5cGVdIHx8ICdpbmZvJwogICAgfSwKICAgIC8vIOiOt+WPlumavuW6puWQjeensAogICAgZ2V0RGlmZmljdWx0eU5hbWUoZGlmZmljdWx0eSkgewogICAgICBjb25zdCBkaWZmaWN1bHR5TWFwID0gewogICAgICAgIC8vIOWtl+espuS4suagvOW8jwogICAgICAgICfnroDljZUnOiAn566A5Y2VJywKICAgICAgICAn5Lit562JJzogJ+S4reetiScsCiAgICAgICAgJ+WbsOmavic6ICflm7Dpmr4nLAogICAgICAgIC8vIOaVsOWtl+agvOW8jwogICAgICAgIDE6ICfnroDljZUnLAogICAgICAgIDI6ICfkuK3nrYknLAogICAgICAgIDM6ICflm7Dpmr4nCiAgICAgIH0KICAgICAgcmV0dXJuIGRpZmZpY3VsdHlNYXBbZGlmZmljdWx0eV0gfHwgJ+S4reetiScKICAgIH0sCiAgICAvLyDojrflj5bliKTmlq3popjnrZTmoYjnsbvlnovvvIjnlKjkuo7moIfnrb7popzoibLvvIkKICAgIGdldEp1ZGdtZW50QW5zd2VyVHlwZShhbnN3ZXIpIHsKICAgICAgY29uc3QgaXNUcnVlID0gYW5zd2VyID09PSAndHJ1ZScgfHwgYW5zd2VyID09PSB0cnVlIHx8IGFuc3dlciA9PT0gMSB8fCBhbnN3ZXIgPT09ICcxJwogICAgICByZXR1cm4gaXNUcnVlID8gJ3N1Y2Nlc3MnIDogJ2RhbmdlcicKICAgIH0sCiAgICAvLyDojrflj5bliKTmlq3popjnrZTmoYjmlofmnKwKICAgIGdldEp1ZGdtZW50QW5zd2VyVGV4dChhbnN3ZXIpIHsKICAgICAgY29uc3QgaXNUcnVlID0gYW5zd2VyID09PSAndHJ1ZScgfHwgYW5zd2VyID09PSB0cnVlIHx8IGFuc3dlciA9PT0gMSB8fCBhbnN3ZXIgPT09ICcxJwogICAgICByZXR1cm4gaXNUcnVlID8gJ+ato+ehricgOiAn6ZSZ6K+vJwogICAgfSwKICAgIC8vIOagvOW8j+WMluaXtumXtAogICAgZm9ybWF0VGltZSh0aW1lKSB7CiAgICAgIGlmICghdGltZSkgcmV0dXJuICctLScKICAgICAgcmV0dXJuIG5ldyBEYXRlKHRpbWUpLnRvTG9jYWxlU3RyaW5nKCkKICAgIH0sCgogICAgLy8g6I635Y+W6YCJ6aG56ZSu5pi+56S6CiAgICBnZXRPcHRpb25LZXkob3B0aW9uLCBpbmRleCkgewogICAgICBjb25zdCBrZXkgPSBvcHRpb24ua2V5IHx8IG9wdGlvbi5vcHRpb25LZXkKCiAgICAgIC8vIOWIpOaWremimOeJueauiuWkhOeQhgogICAgICBpZiAodGhpcy5pc0p1ZGdtZW50UXVlc3Rpb24pIHsKICAgICAgICBpZiAoa2V5ID09PSAndHJ1ZScgfHwga2V5ID09PSB0cnVlKSB7CiAgICAgICAgICByZXR1cm4gJ0EnCiAgICAgICAgfSBlbHNlIGlmIChrZXkgPT09ICdmYWxzZScgfHwga2V5ID09PSBmYWxzZSkgewogICAgICAgICAgcmV0dXJuICdCJwogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g6YCJ5oup6aKY55u05o6l6L+U5Zue5Y6fa2V577yM5aaC5p6c5rKh5pyJa2V55YiZ5L2/55So5a2X5q+N5bqP5YiXCiAgICAgIGlmIChrZXkpIHsKICAgICAgICByZXR1cm4ga2V5CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuIFN0cmluZy5mcm9tQ2hhckNvZGUoNjUgKyBpbmRleCkgLy8gQSwgQiwgQywgRC4uLgogICAgICB9CiAgICB9LAoKICAgIC8vIOWIpOaWreaYr+WQpuS4uuato+ehrumAiemhuQogICAgaXNDb3JyZWN0T3B0aW9uKG9yaWdpbmFsS2V5KSB7CiAgICAgIGlmICghb3JpZ2luYWxLZXkpIHJldHVybiBmYWxzZQoKICAgICAgLy8g6aaW5YWI5bCd6K+V5LuO6YCJ6aG555qEIGlzQ29ycmVjdCDlrZfmrrXliKTmlq0KICAgICAgY29uc3Qgb3B0aW9uID0gdGhpcy52YWxpZE9wdGlvbnMuZmluZChvcHQgPT4KICAgICAgICAob3B0LmtleSB8fCBvcHQub3B0aW9uS2V5KSA9PT0gb3JpZ2luYWxLZXkKICAgICAgKQogICAgICBpZiAob3B0aW9uICYmIG9wdGlvbi5pc0NvcnJlY3QgIT09IHVuZGVmaW5lZCkgewogICAgICAgIHJldHVybiBvcHRpb24uaXNDb3JyZWN0ID09PSB0cnVlCiAgICAgIH0KCiAgICAgIC8vIOWmguaenOayoeaciSBpc0NvcnJlY3Qg5a2X5q6177yM5YiZ5L2/55SoIGNvcnJlY3RBbnN3ZXIg5a2X5q61CiAgICAgIGlmICghdGhpcy5xdWVzdGlvbi5jb3JyZWN0QW5zd2VyKSByZXR1cm4gZmFsc2UKCiAgICAgIC8vIOWkhOeQhuWNlemAiemimO+8iOWtl+espuS4suagvOW8j++8midzaW5nbGUnIOaIluaVsOWtl+agvOW8j++8mjHvvIkKICAgICAgaWYgKHRoaXMucXVlc3Rpb24ucXVlc3Rpb25UeXBlID09PSAnc2luZ2xlJyB8fCB0aGlzLnF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSA9PT0gMSkgewogICAgICAgIHJldHVybiB0aGlzLnF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIudG9TdHJpbmcoKSA9PT0gb3JpZ2luYWxLZXkudG9TdHJpbmcoKQogICAgICB9CiAgICAgIC8vIOWkhOeQhuWkmumAiemimO+8iOWtl+espuS4suagvOW8j++8midtdWx0aXBsZScg5oiW5pWw5a2X5qC85byP77yaMu+8iQogICAgICBlbHNlIGlmICh0aGlzLnF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSA9PT0gJ211bHRpcGxlJyB8fCB0aGlzLnF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSA9PT0gMikgewogICAgICAgIGNvbnN0IGNvcnJlY3RBbnN3ZXJzID0gdGhpcy5xdWVzdGlvbi5jb3JyZWN0QW5zd2VyLnRvU3RyaW5nKCkuc3BsaXQoJywnKS5tYXAoYW5zID0+IGFucy50cmltKCkpCiAgICAgICAgcmV0dXJuIGNvcnJlY3RBbnN3ZXJzLmluY2x1ZGVzKG9yaWdpbmFsS2V5LnRvU3RyaW5nKCkpCiAgICAgIH0KICAgICAgcmV0dXJuIGZhbHNlCiAgICB9LAogICAgLy8g5YiH5o2i5bGV5byA54q25oCBCiAgICB0b2dnbGVFeHBhbmQoKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3RvZ2dsZS1leHBhbmQnLCB0aGlzLnF1ZXN0aW9uLnF1ZXN0aW9uSWQpCiAgICB9LAogICAgLy8g57yW6L6R6aKY55uuCiAgICBoYW5kbGVFZGl0KCkgewogICAgICB0aGlzLiRlbWl0KCdlZGl0JywgdGhpcy5xdWVzdGlvbikKICAgIH0sCiAgICAvLyDlpI3liLbpopjnm64KICAgIGhhbmRsZUNvcHkoKSB7CiAgICAgIHRoaXMuJGVtaXQoJ2NvcHknLCB0aGlzLnF1ZXN0aW9uKQogICAgfSwKICAgIC8vIOWIoOmZpOmimOebrgogICAgaGFuZGxlRGVsZXRlKCkgewogICAgICB0aGlzLiRlbWl0KCdkZWxldGUnLCB0aGlzLnF1ZXN0aW9uKQogICAgfSwKCiAgICAvLyDlpITnkIbpgInmi6nnirbmgIHlj5jljJYKICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3RlZCkgewogICAgICB0aGlzLiRlbWl0KCdzZWxlY3Rpb24tY2hhbmdlJywgdGhpcy5xdWVzdGlvbi5xdWVzdGlvbklkLCBzZWxlY3RlZCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["QuestionCard.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "QuestionCard.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <div class=\"question-card\">\n    <!-- 题目头部 -->\n    <div class=\"question-header\">\n      <div class=\"header-left\">\n        <el-checkbox\n          :value=\"selected\"\n          @change=\"handleSelectionChange\"\n          style=\"margin-right: 12px;\"\n        ></el-checkbox>\n        <span class=\"question-number\">{{ index }}.</span>\n        <span class=\"question-type-bracket\">[{{ getQuestionTypeName(question.questionType) }}]</span>\n        <span class=\"difficulty-label\">难度系数: {{ getDifficultyName(question.difficulty) }}</span>\n        <span class=\"create-time\">创建时间: {{ formatTime(question.createTime) }}</span>\n      </div>\n      <div class=\"header-right\">\n        <el-button\n          type=\"text\"\n          :icon=\"expanded ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpand\"\n          size=\"small\"\n        >\n          {{ expanded ? '收起' : '展开' }}\n        </el-button>\n        <el-button\n          type=\"text\"\n          icon=\"el-icon-edit\"\n          @click=\"handleEdit\"\n          size=\"small\"\n        >\n          编辑\n        </el-button>\n        <el-button\n          type=\"text\"\n          icon=\"el-icon-copy-document\"\n          @click=\"handleCopy\"\n          size=\"small\"\n        >\n          复制\n        </el-button>\n        <el-button\n          type=\"text\"\n          icon=\"el-icon-delete\"\n          @click=\"handleDelete\"\n          size=\"small\"\n          style=\"color: #F56C6C;\"\n        >\n          删除\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 题目内容 -->\n    <div class=\"question-content\">\n      <div v-html=\"question.questionContent\"></div>\n    </div>\n\n    <!-- 展开内容 -->\n    <div v-if=\"expanded\" class=\"question-detail\">\n\n\n      <!-- 题目选项（包括选择题和判断题） -->\n      <div v-if=\"validOptions.length > 0\" class=\"answer-section\">\n        <div class=\"options-list\">\n          <div\n            v-for=\"(option, index) in validOptions\"\n            :key=\"option.key || option.optionKey\"\n            class=\"option-item\"\n            :class=\"{ 'correct-option': isCorrectOption(option.key || option.optionKey) }\"\n          >\n            <span class=\"option-key\">{{ getOptionKey(option, index) }}.</span>\n            <span class=\"option-content\" v-html=\"option.content || option.optionContent\"></span>\n            <span v-if=\"isCorrectOption(option.key || option.optionKey)\" class=\"correct-mark\">✓</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 题目解析 -->\n      <div v-if=\"question.explanation || question.analysis\" class=\"explanation-section\">\n        <div class=\"explanation-text\" v-html=\"question.explanation || question.analysis\"></div>\n      </div>\n\n\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"QuestionCard\",\n  props: {\n    question: {\n      type: Object,\n      required: true\n    },\n    index: {\n      type: Number,\n      required: true\n    },\n    expanded: {\n      type: Boolean,\n      default: false\n    },\n    selected: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {\n    isChoiceQuestion() {\n      return ['single', 'multiple', 1, 2].includes(this.question.questionType)\n    },\n    isJudgmentQuestion() {\n      return ['judgment', 3].includes(this.question.questionType)\n    },\n    // 过滤有效的选项\n    validOptions() {\n      let options = this.question.options\n\n      // 如果options是字符串，需要解析为JSON\n      if (typeof options === 'string') {\n        try {\n          options = JSON.parse(options)\n        } catch (e) {\n          return []\n        }\n      }\n\n      if (!options || !Array.isArray(options)) {\n        return []\n      }\n\n      return options.filter(option => {\n        // 检查选项是否有效\n        const key = option.key || option.optionKey\n        const content = option.content || option.optionContent\n\n        const hasKey = key && key.toString().trim()\n        // 内容可以为空，但key必须存在\n        const hasValidKey = hasKey\n        // 如果内容为空或只包含HTML标签，则过滤掉\n        const hasValidContent = content && content.toString().trim() &&\n                               content.toString().replace(/<[^>]*>/g, '').trim()\n\n        return hasValidKey && hasValidContent\n      })\n    }\n  },\n  methods: {\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        // 字符串格式\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题',\n        // 数字格式\n        1: '单选题',\n        2: '多选题',\n        3: '判断题'\n      }\n      return typeMap[type] || '未知题型'\n    },\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        // 字符串格式\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning',\n        // 数字格式\n        1: 'primary',\n        2: 'success',\n        3: 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n    // 获取难度名称\n    getDifficultyName(difficulty) {\n      const difficultyMap = {\n        // 字符串格式\n        '简单': '简单',\n        '中等': '中等',\n        '困难': '困难',\n        // 数字格式\n        1: '简单',\n        2: '中等',\n        3: '困难'\n      }\n      return difficultyMap[difficulty] || '中等'\n    },\n    // 获取判断题答案类型（用于标签颜色）\n    getJudgmentAnswerType(answer) {\n      const isTrue = answer === 'true' || answer === true || answer === 1 || answer === '1'\n      return isTrue ? 'success' : 'danger'\n    },\n    // 获取判断题答案文本\n    getJudgmentAnswerText(answer) {\n      const isTrue = answer === 'true' || answer === true || answer === 1 || answer === '1'\n      return isTrue ? '正确' : '错误'\n    },\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return '--'\n      return new Date(time).toLocaleString()\n    },\n\n    // 获取选项键显示\n    getOptionKey(option, index) {\n      const key = option.key || option.optionKey\n\n      // 判断题特殊处理\n      if (this.isJudgmentQuestion) {\n        if (key === 'true' || key === true) {\n          return 'A'\n        } else if (key === 'false' || key === false) {\n          return 'B'\n        }\n      }\n\n      // 选择题直接返回原key，如果没有key则使用字母序列\n      if (key) {\n        return key\n      } else {\n        return String.fromCharCode(65 + index) // A, B, C, D...\n      }\n    },\n\n    // 判断是否为正确选项\n    isCorrectOption(originalKey) {\n      if (!originalKey) return false\n\n      // 首先尝试从选项的 isCorrect 字段判断\n      const option = this.validOptions.find(opt =>\n        (opt.key || opt.optionKey) === originalKey\n      )\n      if (option && option.isCorrect !== undefined) {\n        return option.isCorrect === true\n      }\n\n      // 如果没有 isCorrect 字段，则使用 correctAnswer 字段\n      if (!this.question.correctAnswer) return false\n\n      // 处理单选题（字符串格式：'single' 或数字格式：1）\n      if (this.question.questionType === 'single' || this.question.questionType === 1) {\n        return this.question.correctAnswer.toString() === originalKey.toString()\n      }\n      // 处理多选题（字符串格式：'multiple' 或数字格式：2）\n      else if (this.question.questionType === 'multiple' || this.question.questionType === 2) {\n        const correctAnswers = this.question.correctAnswer.toString().split(',').map(ans => ans.trim())\n        return correctAnswers.includes(originalKey.toString())\n      }\n      return false\n    },\n    // 切换展开状态\n    toggleExpand() {\n      this.$emit('toggle-expand', this.question.questionId)\n    },\n    // 编辑题目\n    handleEdit() {\n      this.$emit('edit', this.question)\n    },\n    // 复制题目\n    handleCopy() {\n      this.$emit('copy', this.question)\n    },\n    // 删除题目\n    handleDelete() {\n      this.$emit('delete', this.question)\n    },\n\n    // 处理选择状态变化\n    handleSelectionChange(selected) {\n      this.$emit('selection-change', this.question.questionId, selected)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.question-card {\n  background: #fff;\n  border-radius: 6px;\n  box-shadow: 0 2px 6px rgba(0,0,0,0.08);\n  margin-bottom: 12px;\n  overflow: hidden;\n}\n\n.question-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.question-number {\n  font-weight: bold;\n  font-size: 15px;\n  color: #333;\n}\n\n.question-type-bracket {\n  color: #409eff;\n  font-weight: 500;\n  font-size: 13px;\n}\n\n.difficulty-label {\n  font-size: 13px;\n  color: #666;\n}\n\n.create-time {\n  font-size: 11px;\n  color: #999;\n}\n\n.header-right {\n  display: flex;\n  gap: 5px;\n}\n\n.question-preview {\n  padding: 16px 20px;\n}\n\n.question-content {\n  padding: 12px 16px 12px 50px;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n  background: #fafafa;\n}\n\n.question-detail {\n  border-top: 1px solid #e9ecef;\n  padding: 20px;\n}\n\n.detail-section {\n  margin-bottom: 20px;\n}\n\n.detail-section:last-child {\n  margin-bottom: 0;\n}\n\n.detail-section h4 {\n  margin: 0 0 12px 0;\n  font-size: 16px;\n  color: #333;\n  font-weight: 600;\n}\n\n.content-text, .explanation-text {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.options-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  background: #fff;\n  border-radius: 6px;\n  gap: 12px;\n  border: 1px solid #e4e7ed;\n  transition: all 0.3s ease;\n}\n\n.option-item:hover {\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.option-item.correct-option {\n  background: #f0f9ff;\n  border: 1px solid #67c23a;\n  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.2);\n}\n\n.option-key {\n  font-weight: bold;\n  color: #409eff;\n  min-width: 24px;\n  font-size: 15px;\n}\n\n.option-content {\n  flex: 1;\n  font-size: 15px;\n  color: #333;\n  line-height: 1.6;\n}\n\n.correct-mark {\n  color: #67c23a;\n  font-weight: bold;\n  font-size: 16px;\n}\n\n.answer-section {\n  padding: 20px;\n  background: #f9f9f9;\n}\n\n.judgment-answer {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: #fff;\n  border-radius: 4px;\n}\n\n.answer-label {\n  font-weight: bold;\n  color: #333;\n}\n\n.answer-value {\n  font-weight: 500;\n}\n\n.answer-value.success {\n  color: #67c23a;\n}\n\n.answer-value.danger {\n  color: #f56c6c;\n}\n\n.explanation-section {\n  padding: 20px;\n  border-top: 1px solid #e9ecef;\n  background: #f5f5f5;\n}\n\n.explanation-text {\n  font-size: 14px;\n  line-height: 1.8;\n  color: #666;\n  background: #fff;\n  padding: 16px;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.explanation-text::before {\n  content: \"解析：\";\n  font-weight: bold;\n  color: #409eff;\n  display: block;\n  margin-bottom: 8px;\n}\n</style>\n"]}]}